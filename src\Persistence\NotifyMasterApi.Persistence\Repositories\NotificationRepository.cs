using Microsoft.EntityFrameworkCore;
using NotifyMasterApi.Infrastructure.Entities;
using NotifyMasterApi.Infrastructure.Interfaces;
using NotifyMasterApi.Persistence.Data;
using NotificationContract.Enums;

namespace NotifyMasterApi.Persistence.Repositories;

/// <summary>
/// Repository implementation for notification log operations.
/// Provides data access methods for notification tracking and management.
/// </summary>
public class NotificationRepository : INotificationRepository
{
    private readonly NotificationDbContext _context;

    /// <summary>
    /// Initializes a new instance of the NotificationRepository.
    /// </summary>
    /// <param name="context">The database context.</param>
    public NotificationRepository(NotificationDbContext context)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
    }

    /// <inheritdoc />
    public async Task<NotificationLog> AddAsync(NotificationLog notificationLog, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(notificationLog);
        
        notificationLog.CreatedAt = DateTime.UtcNow;
        
        var entry = await _context.NotificationLogs.AddAsync(notificationLog, cancellationToken);
        return entry.Entity;
    }

    /// <inheritdoc />
    public async Task<NotificationLog> UpdateAsync(NotificationLog notificationLog, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(notificationLog);
        
        var entry = _context.NotificationLogs.Update(notificationLog);
        return entry.Entity;
    }

    /// <inheritdoc />
    public async Task<NotificationLog?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.NotificationLogs
            .FirstOrDefaultAsync(x => x.Id == id, cancellationToken);
    }

    /// <inheritdoc />
    public async Task<NotificationLog?> GetByMessageIdAsync(string messageId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(messageId);
        
        return await _context.NotificationLogs
            .FirstOrDefaultAsync(x => x.MessageId == messageId, cancellationToken);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<NotificationLog>> GetAsync(
        NotificationType? type = null,
        NotificationStatus? status = null,
        string? provider = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        int skip = 0,
        int take = 100,
        CancellationToken cancellationToken = default)
    {
        var query = _context.NotificationLogs.AsQueryable();

        if (type.HasValue)
            query = query.Where(x => x.Type == type.Value);

        if (status.HasValue)
            query = query.Where(x => x.Status == status.Value);

        if (!string.IsNullOrWhiteSpace(provider))
            query = query.Where(x => x.Provider == provider);

        if (fromDate.HasValue)
            query = query.Where(x => x.CreatedAt >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(x => x.CreatedAt <= toDate.Value);

        return await query
            .OrderByDescending(x => x.CreatedAt)
            .Skip(skip)
            .Take(take)
            .ToListAsync(cancellationToken);
    }

    /// <inheritdoc />
    public async Task<int> GetCountAsync(
        NotificationType? type = null,
        NotificationStatus? status = null,
        string? provider = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.NotificationLogs.AsQueryable();

        if (type.HasValue)
            query = query.Where(x => x.Type == type.Value);

        if (status.HasValue)
            query = query.Where(x => x.Status == status.Value);

        if (!string.IsNullOrWhiteSpace(provider))
            query = query.Where(x => x.Provider == provider);

        if (fromDate.HasValue)
            query = query.Where(x => x.CreatedAt >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(x => x.CreatedAt <= toDate.Value);

        return await query.CountAsync(cancellationToken);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<NotificationLog>> GetForRetryAsync(
        int maxRetryCount = 3,
        TimeSpan? retryAfter = null,
        int take = 100,
        CancellationToken cancellationToken = default)
    {
        var query = _context.NotificationLogs
            .Where(x => x.Status == NotificationStatus.Failed && x.RetryCount < maxRetryCount);

        if (retryAfter.HasValue)
        {
            var cutoffTime = DateTime.UtcNow - retryAfter.Value;
            query = query.Where(x => x.LastRetryAt == null || x.LastRetryAt <= cutoffTime);
        }

        return await query
            .OrderBy(x => x.CreatedAt)
            .Take(take)
            .ToListAsync(cancellationToken);
    }

    /// <inheritdoc />
    public async Task<int> DeleteOldLogsAsync(DateTime olderThan, CancellationToken cancellationToken = default)
    {
        var logsToDelete = await _context.NotificationLogs
            .Where(x => x.CreatedAt < olderThan)
            .ToListAsync(cancellationToken);

        _context.NotificationLogs.RemoveRange(logsToDelete);
        return logsToDelete.Count;
    }
}
