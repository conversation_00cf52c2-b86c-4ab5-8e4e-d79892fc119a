using Microsoft.Extensions.DependencyInjection;
using PluginContract.Interfaces;
using PluginCore.Services;

namespace PluginCore.Extensions;

/// <summary>
///  Extension methods for adding the plugin system to the service collection
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    ///  Adds the plugin system to the service collection
    /// </summary>
    /// <param name="services"> Service collection to add to </param>
    /// <returns> Service collection </returns>
    public static IServiceCollection AddPluginSystem(this IServiceCollection services)
    {
        services.AddSingleton<IPluginManager, PluginManager>();
        services.AddScoped<NotificationService>();

        return services;
    }
}
