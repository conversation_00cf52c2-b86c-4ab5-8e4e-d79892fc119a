using Microsoft.Extensions.Logging;
using NotificationContract.Models;
using PluginCore.Interfaces;
using PluginCore.Models;
using FirebaseAdmin;
using FirebaseAdmin.Messaging;
using Google.Apis.Auth.OAuth2;
using System.Text.Json;

namespace Plugin.Push.Firebase;

public class FirebasePlugin : IPushPlugin
{
    private readonly ILogger<FirebasePlugin> _logger;
    private Dictionary<string, PluginConfigurationItem> _configuration = new();
    private FirebaseApp? _firebaseApp;
    private bool _isInitialized = false;

    public FirebasePlugin(ILogger<FirebasePlugin> logger)
    {
        _logger = logger;
    }

    public string Name => "Firebase Push Plugin";
    public string Version => "1.0.0";
    public string Provider => "Firebase";

    public async Task InitializeAsync(Dictionary<string, PluginConfigurationItem> configuration)
    {
        _configuration = configuration;
        
        var serviceAccountJson = GetConfigValue<string>("serviceAccountJson");
        var projectId = GetConfigValue<string>("projectId");

        if (string.IsNullOrEmpty(serviceAccountJson))
        {
            throw new InvalidOperationException("Firebase Service Account JSON is required");
        }

        if (string.IsNullOrEmpty(projectId))
        {
            throw new InvalidOperationException("Firebase Project ID is required");
        }

        try
        {
            // Parse service account JSON
            var credential = GoogleCredential.FromJson(serviceAccountJson);
            
            _firebaseApp = FirebaseApp.Create(new AppOptions()
            {
                Credential = credential,
                ProjectId = projectId
            }, $"NotificationService_{Guid.NewGuid()}");

            _isInitialized = true;
            _logger.LogInformation("Firebase plugin initialized successfully for project: {ProjectId}", projectId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize Firebase plugin");
            throw;
        }
    }

    public async Task<PushResponse> SendAsync(PushMessageRequest request)
    {
        if (!_isInitialized || _firebaseApp == null)
            throw new InvalidOperationException("Plugin not initialized");

        try
        {
            var messaging = FirebaseMessaging.GetMessaging(_firebaseApp);

            var message = new Message()
            {
                Token = request.DeviceToken,
                Notification = new Notification()
                {
                    Title = request.Title,
                    Body = request.Message,
                    ImageUrl = request.ImageUrl
                },
                Data = request.Data ?? new Dictionary<string, string>(),
                Android = CreateAndroidConfig(request),
                Apns = CreateApnsConfig(request),
                Webpush = CreateWebpushConfig(request)
            };

            var response = await messaging.SendAsync(message);
            
            _logger.LogInformation("Push notification sent via Firebase. MessageId: {MessageId}", response);
            return new PushResponse(true, MessageId: response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending push notification via Firebase");
            return new PushResponse(false, ErrorMessage: ex.Message);
        }
    }

    public async Task<BulkPushResponse> SendBulkAsync(BulkPushRequest request)
    {
        if (!_isInitialized || _firebaseApp == null)
            throw new InvalidOperationException("Plugin not initialized");

        try
        {
            var messaging = FirebaseMessaging.GetMessaging(_firebaseApp);
            var messages = new List<Message>();

            foreach (var pushRequest in request.Messages)
            {
                var message = new Message()
                {
                    Token = pushRequest.DeviceToken,
                    Notification = new Notification()
                    {
                        Title = pushRequest.Title,
                        Body = pushRequest.Message,
                        ImageUrl = pushRequest.ImageUrl
                    },
                    Data = pushRequest.Data ?? new Dictionary<string, string>(),
                    Android = CreateAndroidConfig(pushRequest),
                    Apns = CreateApnsConfig(pushRequest),
                    Webpush = CreateWebpushConfig(pushRequest)
                };
                messages.Add(message);
            }

            var response = await messaging.SendEachAsync(messages);
            
            var results = new List<PushResponse>();
            for (int i = 0; i < response.Responses.Count; i++)
            {
                var sendResponse = response.Responses[i];
                if (sendResponse.IsSuccess)
                {
                    results.Add(new PushResponse(true, MessageId: sendResponse.MessageId));
                }
                else
                {
                    results.Add(new PushResponse(false, ErrorMessage: sendResponse.Exception?.Message ?? "Unknown error"));
                }
            }

            var successCount = results.Count(r => r.IsSuccess);
            return new BulkPushResponse(
                successCount == results.Count,
                SuccessCount: successCount,
                FailureCount: results.Count - successCount,
                Results: results
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending bulk push notifications via Firebase");
            return new BulkPushResponse(false, ErrorMessage: ex.Message);
        }
    }

    public async Task<MessageStatusResponse> GetMessageStatusAsync(string messageId)
    {
        // Firebase doesn't provide direct message status API
        return new MessageStatusResponse(false, ErrorMessage: "Message status not available via Firebase FCM API");
    }

    public async Task<MessageHistoryResponse> GetMessageHistoryAsync(string deviceToken)
    {
        // Firebase doesn't provide message history API
        return new MessageHistoryResponse(false, ErrorMessage: "Message history not available via Firebase FCM API");
    }

    public async Task<PushResponse> ResendMessageAsync(string messageId)
    {
        // Firebase doesn't support direct message resending
        return new PushResponse(false, ErrorMessage: "Message resending not supported by Firebase FCM");
    }

    public async Task<bool> ValidateConfigurationAsync()
    {
        try
        {
            var serviceAccountJson = GetConfigValue<string>("serviceAccountJson");
            var projectId = GetConfigValue<string>("projectId");
            
            if (string.IsNullOrEmpty(serviceAccountJson) || string.IsNullOrEmpty(projectId))
                return false;

            // Test Firebase connection by creating a temporary app
            var credential = GoogleCredential.FromJson(serviceAccountJson);
            var testApp = FirebaseApp.Create(new AppOptions()
            {
                Credential = credential,
                ProjectId = projectId
            }, $"ValidationTest_{Guid.NewGuid()}");

            // Clean up test app
            testApp.Delete();
            
            return true;
        }
        catch
        {
            return false;
        }
    }

    private AndroidConfig? CreateAndroidConfig(PushMessageRequest request)
    {
        var priority = GetConfigValue<string>("androidPriority", "high");
        var ttl = GetConfigValue<int>("androidTtl", 3600);

        return new AndroidConfig()
        {
            Priority = priority == "high" ? Priority.High : Priority.Normal,
            Ttl = TimeSpan.FromSeconds(ttl),
            Notification = new AndroidNotification()
            {
                Title = request.Title,
                Body = request.Message,
                Icon = GetConfigValue<string>("androidIcon"),
                Color = GetConfigValue<string>("androidColor"),
                Sound = GetConfigValue<string>("androidSound", "default"),
                ChannelId = GetConfigValue<string>("androidChannelId", "default")
            }
        };
    }

    private ApnsConfig? CreateApnsConfig(PushMessageRequest request)
    {
        var badge = GetConfigValue<int?>("iosBadge");
        var sound = GetConfigValue<string>("iosSound", "default");

        return new ApnsConfig()
        {
            Aps = new Aps()
            {
                Alert = new ApsAlert()
                {
                    Title = request.Title,
                    Body = request.Message
                },
                Badge = badge,
                Sound = sound,
                ContentAvailable = GetConfigValue<bool>("iosContentAvailable", false),
                MutableContent = GetConfigValue<bool>("iosMutableContent", false)
            }
        };
    }

    private WebpushConfig? CreateWebpushConfig(PushMessageRequest request)
    {
        var icon = GetConfigValue<string>("webIcon");
        var badge = GetConfigValue<string>("webBadge");

        return new WebpushConfig()
        {
            Notification = new WebpushNotification()
            {
                Title = request.Title,
                Body = request.Message,
                Icon = icon,
                Badge = badge,
                Image = request.ImageUrl
            }
        };
    }

    private T? GetConfigValue<T>(string key, T? defaultValue = default)
    {
        if (_configuration.TryGetValue(key, out var configItem))
        {
            if (configItem.DefaultValue != null)
            {
                return (T)Convert.ChangeType(configItem.DefaultValue, typeof(T));
            }
        }
        return defaultValue;
    }

    public void Dispose()
    {
        _firebaseApp?.Delete();
    }
}
