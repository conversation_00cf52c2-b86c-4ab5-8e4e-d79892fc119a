using FastEndpoints;
using Microsoft.AspNetCore.Authorization;
using NotifyMasterApi.Authentication;
using NotifyMasterApi.Documentation;
using NotificationContract.Models;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;

namespace NotifyMasterApi.Endpoints.Auth;

/// <summary>
/// User login endpoint using FastEndpoints
/// </summary>
[HttpPost("/auth/login"), AllowAnonymous]
public class LoginEndpoint : Endpoint<LoginRequest, ApiResponse<LoginResponse>>
{
    private readonly IJwtAuthenticationService _jwtService;
    private readonly IUserService _userService;

    public LoginEndpoint(IJwtAuthenticationService jwtService, IUserService userService)
    {
        _jwtService = jwtService;
        _userService = userService;
    }

    public override void Configure()
    {
        Post("/auth/login");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "🔐 User Login";
            s.Description = "Authenticate user and receive JWT token for API access";
            s.ExampleRequest = new LoginRequest
            {
                Username = "admin",
                Password = "password123"
            };
        });
        
        Description(builder => builder
            .Accepts<LoginRequest>("application/json")
            .Produces<ApiResponse<LoginResponse>>(200, "application/json")
            .ProducesProblem(400)
            .ProducesProblem(401)
            .ProducesProblem(500));
    }

    public override async Task HandleAsync(LoginRequest req, CancellationToken ct)
    {
        try
        {
            // Validate user credentials
            var user = await _userService.ValidateUserAsync(req.Username, req.Password);
            
            if (user == null)
            {
                await SendAsync(new ApiResponse<LoginResponse>
                {
                    Success = false,
                    Message = "Invalid username or password",
                    Data = null,
                    RequestId = HttpContext.TraceIdentifier,
                    Timestamp = DateTime.UtcNow
                }, 401, ct);
                return;
            }

            // Generate JWT token
            var token = _jwtService.GenerateToken(user.Id, user.Username, user.Roles.ToArray(), user.Permissions.ToArray());

            await SendOkAsync(new ApiResponse<LoginResponse>
            {
                Success = true,
                Message = "Login successful",
                Data = new LoginResponse
                {
                    Token = token,
                    Username = user.Username,
                    Roles = user.Roles,
                    ExpiresAt = DateTime.UtcNow.AddHours(24) // Assuming 24-hour token expiry
                },
                RequestId = HttpContext.TraceIdentifier,
                Timestamp = DateTime.UtcNow
            }, ct);
        }
        catch (Exception ex)
        {
            await SendAsync(new ApiResponse<LoginResponse>
            {
                Success = false,
                Message = "An error occurred during login",
                Data = new { Error = ex.Message },
                RequestId = HttpContext.TraceIdentifier,
                Timestamp = DateTime.UtcNow
            }, 500, ct);
        }
    }
}

/// <summary>
/// Create API key endpoint using FastEndpoints
/// </summary>
[HttpPost("/auth/api-key"), Authorize]
public class CreateApiKeyEndpoint : Endpoint<CreateApiKeyRequest, ApiResponse<CreateApiKeyResponse>>
{
    private readonly IApiKeyService _apiKeyService;

    public CreateApiKeyEndpoint(IApiKeyService apiKeyService)
    {
        _apiKeyService = apiKeyService;
    }

    public override void Configure()
    {
        Post("/auth/api-key");
        Policies("JwtPolicy");
        Summary(s =>
        {
            s.Summary = "🔑 Create API Key";
            s.Description = "Generate a new API key for programmatic access";
            s.ExampleRequest = new CreateApiKeyRequest
            {
                Name = "Production API Key",
                Permissions = ["email:send", "sms:send", "push:send"]
            };
        });
        
        Description(builder => builder
            .Accepts<CreateApiKeyRequest>("application/json")
            .Produces<ApiResponse<CreateApiKeyResponse>>(200, "application/json")
            .ProducesProblem(400)
            .ProducesProblem(401)
            .ProducesProblem(500));
    }

    public override async Task HandleAsync(CreateApiKeyRequest req, CancellationToken ct)
    {
        try
        {
            var username = User.Identity?.Name ?? "unknown";
            var apiKey = await _apiKeyService.CreateApiKeyAsync(req.Name, new[] { "User" }, req.Permissions.ToArray());

            await SendOkAsync(new ApiResponse<CreateApiKeyResponse>
            {
                Success = true,
                Message = "API key created successfully",
                Data = new CreateApiKeyResponse
                {
                    ApiKey = apiKey.Id, // Use the ID as the key for now
                    Name = apiKey.Name,
                    Permissions = apiKey.Permissions.ToList(),
                    CreatedAt = apiKey.CreatedAt,
                    ExpiresAt = apiKey.ExpiresAt
                },
                RequestId = HttpContext.TraceIdentifier,
                Timestamp = DateTime.UtcNow
            }, ct);
        }
        catch (Exception ex)
        {
            await SendAsync(new ApiResponse<CreateApiKeyResponse>
            {
                Success = false,
                Message = "An error occurred while creating API key",
                Data = new { Error = ex.Message },
                RequestId = HttpContext.TraceIdentifier,
                Timestamp = DateTime.UtcNow
            }, 500, ct);
        }
    }
}

/// <summary>
/// Login request model
/// </summary>
public class LoginRequest
{
    /// <summary>
    /// Username for authentication
    /// </summary>
    [Required]
    [Description("Username for authentication")]
    public string Username { get; set; } = "";

    /// <summary>
    /// Password for authentication
    /// </summary>
    [Required]
    [Description("Password for authentication")]
    public string Password { get; set; } = "";
}

/// <summary>
/// Login response model
/// </summary>
public class LoginResponse
{
    /// <summary>
    /// JWT authentication token
    /// </summary>
    [Description("JWT token for API authentication")]
    public string Token { get; set; } = "";

    /// <summary>
    /// Authenticated username
    /// </summary>
    [Description("Username of the authenticated user")]
    public string Username { get; set; } = "";

    /// <summary>
    /// User roles
    /// </summary>
    [Description("List of roles assigned to the user")]
    public List<string> Roles { get; set; } = new();

    /// <summary>
    /// Token expiration time
    /// </summary>
    [Description("UTC timestamp when the token expires")]
    public DateTime ExpiresAt { get; set; }
}

/// <summary>
/// Create API key request model
/// </summary>
public class CreateApiKeyRequest
{
    /// <summary>
    /// Name for the API key
    /// </summary>
    [Required]
    [Description("Descriptive name for the API key")]
    public string Name { get; set; } = "";

    /// <summary>
    /// Permissions for the API key
    /// </summary>
    [Description("List of permissions granted to the API key")]
    public List<string> Permissions { get; set; } = new();
}

/// <summary>
/// Create API key response model
/// </summary>
public class CreateApiKeyResponse
{
    /// <summary>
    /// Generated API key
    /// </summary>
    [Description("The generated API key (store securely)")]
    public string ApiKey { get; set; } = "";

    /// <summary>
    /// Name of the API key
    /// </summary>
    [Description("Descriptive name for the API key")]
    public string Name { get; set; } = "";

    /// <summary>
    /// Permissions granted to the API key
    /// </summary>
    [Description("List of permissions granted to the API key")]
    public List<string> Permissions { get; set; } = new();

    /// <summary>
    /// Creation timestamp
    /// </summary>
    [Description("UTC timestamp when the API key was created")]
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Expiration timestamp
    /// </summary>
    [Description("UTC timestamp when the API key expires")]
    public DateTime? ExpiresAt { get; set; }
}
