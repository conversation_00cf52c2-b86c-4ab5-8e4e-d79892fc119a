{"name": "BulkSms SMS Plugin", "version": "1.0.0", "description": "SMS plugin for BulkSms provider", "author": "BlackBee Software Team", "type": "Sms", "provider": "BulkSms", "assemblyName": "Plugin.Sms.BulkSms.dll", "entryPoint": "Plugin.Sms.BulkSms.BulkSmsPlugin", "dependencies": [{"name": "Microsoft.Extensions.Http", "version": "9.0.0", "isRequired": true}], "configuration": {"apiUrl": {"type": "string", "description": "BulkSms API URL", "defaultValue": "https://api.bulksms.com/v1", "isRequired": true, "isSecret": false}, "username": {"type": "string", "description": "BulkSms API Username", "isRequired": true, "isSecret": false}, "password": {"type": "string", "description": "BulkSms API Password", "isRequired": true, "isSecret": true}, "from": {"type": "string", "description": "Default sender ID", "isRequired": false, "isSecret": false}, "timeout": {"type": "int", "description": "HTTP timeout in seconds", "defaultValue": 30, "isRequired": false, "isSecret": false}}, "supportedFeatures": ["SendSms", "BulkSms", "MessageStatus", "MessageHistory"], "minimumFrameworkVersion": "net9.0", "isEnabled": true, "priority": 100, "metadata": {"website": "https://www.bulksms.com", "documentation": "https://www.bulksms.com/developer/json/v1/"}}