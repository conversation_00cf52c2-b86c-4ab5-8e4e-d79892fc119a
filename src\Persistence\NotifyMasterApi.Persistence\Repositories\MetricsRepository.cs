using Microsoft.EntityFrameworkCore;
using NotifyMasterApi.Infrastructure.Entities;
using NotifyMasterApi.Infrastructure.Interfaces;
using NotifyMasterApi.Persistence.Data;
using NotificationContract.Enums;

namespace NotifyMasterApi.Persistence.Repositories;

/// <summary>
/// Repository implementation for notification metrics operations.
/// Provides data access methods for metrics tracking and reporting.
/// </summary>
public class MetricsRepository : IMetricsRepository
{
    private readonly NotificationDbContext _context;

    /// <summary>
    /// Initializes a new instance of the MetricsRepository.
    /// </summary>
    /// <param name="context">The database context.</param>
    public MetricsRepository(NotificationDbContext context)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
    }

    /// <inheritdoc />
    public async Task<NotificationMetrics> AddAsync(NotificationMetrics metrics, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(metrics);
        
        metrics.CreatedAt = DateTime.UtcNow;
        metrics.UpdatedAt = DateTime.UtcNow;
        
        var entry = await _context.NotificationMetrics.AddAsync(metrics, cancellationToken);
        return entry.Entity;
    }

    /// <inheritdoc />
    public async Task<NotificationMetrics> UpdateAsync(NotificationMetrics metrics, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(metrics);
        
        metrics.UpdatedAt = DateTime.UtcNow;
        
        var entry = _context.NotificationMetrics.Update(metrics);
        return entry.Entity;
    }

    /// <inheritdoc />
    public async Task<NotificationMetrics?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.NotificationMetrics
            .FirstOrDefaultAsync(x => x.Id == id, cancellationToken);
    }

    /// <inheritdoc />
    public async Task<NotificationMetrics?> GetByTypeProviderDateAsync(
        NotificationType type,
        string provider,
        DateTime date,
        CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(provider);
        
        return await _context.NotificationMetrics
            .FirstOrDefaultAsync(x => x.Type == type && x.Provider == provider && x.Date.Date == date.Date, cancellationToken);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<NotificationMetrics>> GetAsync(
        NotificationType? type = null,
        string? provider = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        int skip = 0,
        int take = 100,
        CancellationToken cancellationToken = default)
    {
        var query = _context.NotificationMetrics.AsQueryable();

        if (type.HasValue)
            query = query.Where(x => x.Type == type.Value);

        if (!string.IsNullOrWhiteSpace(provider))
            query = query.Where(x => x.Provider == provider);

        if (fromDate.HasValue)
            query = query.Where(x => x.Date >= fromDate.Value.Date);

        if (toDate.HasValue)
            query = query.Where(x => x.Date <= toDate.Value.Date);

        return await query
            .OrderByDescending(x => x.Date)
            .ThenBy(x => x.Provider)
            .Skip(skip)
            .Take(take)
            .ToListAsync(cancellationToken);
    }

    /// <inheritdoc />
    public async Task<NotificationMetrics> GetAggregatedAsync(
        NotificationType? type = null,
        string? provider = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.NotificationMetrics.AsQueryable();

        if (type.HasValue)
            query = query.Where(x => x.Type == type.Value);

        if (!string.IsNullOrWhiteSpace(provider))
            query = query.Where(x => x.Provider == provider);

        if (fromDate.HasValue)
            query = query.Where(x => x.Date >= fromDate.Value.Date);

        if (toDate.HasValue)
            query = query.Where(x => x.Date <= toDate.Value.Date);

        var aggregated = await query
            .GroupBy(x => 1) // Group all records together
            .Select(g => new NotificationMetrics
            {
                Id = Guid.NewGuid(),
                Type = type ?? NotificationType.Email, // Default type for aggregation
                Provider = provider ?? "All",
                Date = DateTime.UtcNow.Date,
                TotalSent = g.Sum(x => x.TotalSent),
                TotalDelivered = g.Sum(x => x.TotalDelivered),
                TotalFailed = g.Sum(x => x.TotalFailed),
                TotalRetries = g.Sum(x => x.TotalRetries),
                AverageResponseTime = g.Average(x => x.AverageResponseTime),
                SuccessRate = g.Sum(x => x.TotalSent) > 0 ? (double)g.Sum(x => x.TotalDelivered) / g.Sum(x => x.TotalSent) * 100 : 0,
                FailureRate = g.Sum(x => x.TotalSent) > 0 ? (double)g.Sum(x => x.TotalFailed) / g.Sum(x => x.TotalSent) * 100 : 0,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            })
            .FirstOrDefaultAsync(cancellationToken);

        return aggregated ?? new NotificationMetrics
        {
            Id = Guid.NewGuid(),
            Type = type ?? NotificationType.Email,
            Provider = provider ?? "All",
            Date = DateTime.UtcNow.Date,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };
    }

    /// <inheritdoc />
    public async Task<IEnumerable<NotificationMetrics>> GetTopProvidersAsync(
        NotificationType? type = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        int take = 10,
        CancellationToken cancellationToken = default)
    {
        var query = _context.NotificationMetrics.AsQueryable();

        if (type.HasValue)
            query = query.Where(x => x.Type == type.Value);

        if (fromDate.HasValue)
            query = query.Where(x => x.Date >= fromDate.Value.Date);

        if (toDate.HasValue)
            query = query.Where(x => x.Date <= toDate.Value.Date);

        return await query
            .GroupBy(x => new { x.Type, x.Provider })
            .Select(g => new NotificationMetrics
            {
                Id = Guid.NewGuid(),
                Type = g.Key.Type,
                Provider = g.Key.Provider,
                Date = DateTime.UtcNow.Date,
                TotalSent = g.Sum(x => x.TotalSent),
                TotalDelivered = g.Sum(x => x.TotalDelivered),
                TotalFailed = g.Sum(x => x.TotalFailed),
                TotalRetries = g.Sum(x => x.TotalRetries),
                AverageResponseTime = g.Average(x => x.AverageResponseTime),
                SuccessRate = g.Sum(x => x.TotalSent) > 0 ? (double)g.Sum(x => x.TotalDelivered) / g.Sum(x => x.TotalSent) * 100 : 0,
                FailureRate = g.Sum(x => x.TotalSent) > 0 ? (double)g.Sum(x => x.TotalFailed) / g.Sum(x => x.TotalSent) * 100 : 0,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            })
            .OrderByDescending(x => x.SuccessRate)
            .ThenByDescending(x => x.TotalSent)
            .Take(take)
            .ToListAsync(cancellationToken);
    }

    /// <inheritdoc />
    public async Task<int> DeleteOldMetricsAsync(DateTime olderThan, CancellationToken cancellationToken = default)
    {
        var metricsToDelete = await _context.NotificationMetrics
            .Where(x => x.Date < olderThan.Date)
            .ToListAsync(cancellationToken);

        _context.NotificationMetrics.RemoveRange(metricsToDelete);
        return metricsToDelete.Count;
    }
}
