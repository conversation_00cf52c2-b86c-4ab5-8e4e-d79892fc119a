<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="../Plugin.Common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AssemblyName>Plugin.Sms.Vonage</AssemblyName>
    <RootNamespace>Plugin.Sms.Vonage</RootNamespace>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <Description>Vonage (Nexmo) SMS provider plugin for NotifyMaster</Description>
    <Authors>NotifyMaster Team</Authors>
    <Company>NotifyMaster</Company>
    <Product>NotifyMaster SMS Plugins</Product>
    <Copyright>Copyright © 2024 NotifyMaster</Copyright>
    <Version>1.0.0</Version>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Contracts\SmsContract\SmsContract.csproj" />
    <ProjectReference Include="..\..\Contracts\PluginContract\PluginContract.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Vonage" Version="7.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.0" />
  </ItemGroup>

  <ItemGroup>
    <None Include="manifest.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
