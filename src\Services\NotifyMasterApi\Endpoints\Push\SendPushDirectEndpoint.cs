using FastEndpoints;
using Microsoft.AspNetCore.Authorization;
using NotifyMasterApi.Gateways;
using NotifyMasterApi.Documentation;
using PushNotificationContract.Models;
using NotificationContract.Models;
using System.ComponentModel.DataAnnotations;

namespace NotifyMasterApi.Endpoints.Push;

/// <summary>
/// Send push notification directly (bypass queue) endpoint using FastEndpoints
/// </summary>
[HttpPost("/push/send-direct"), Authorize]
public class SendPushDirectEndpoint : Endpoint<PushMessageRequest, ApiResponse<PushSendResult>>
{
    private readonly IPushGateway _pushGateway;

    public SendPushDirectEndpoint(IPushGateway pushGateway)
    {
        _pushGateway = pushGateway;
    }

    public override void Configure()
    {
        Post("/push/send-direct");
        Policies("ApiKeyPolicy", "JwtPolicy");
        Summary(s =>
        {
            s.Summary = "📱 Send Push Notification Direct (No Queue)";
            s.Description = "Send push notification immediately without queuing for urgent/critical messages";
            s.ExampleRequest = new PushMessageRequest
            {
                DeviceToken = "fcm_token_12345",
                Title = "🚨 Security Alert",
                Body = "Suspicious login detected from IP *************. If this wasn't you, secure your account immediately.",
                Platform = "android",
                Priority = "high",
                ImageUrl = "https://example.com/security-alert.png"
            };
        });
        
        Description(builder => builder
            .Accepts<PushMessageRequest>("application/json")
            .Produces<ApiResponse<PushSendResult>>(200, "application/json")
            .ProducesProblem(400)
            .ProducesProblem(401)
            .ProducesProblem(422)
            .ProducesProblem(429)
            .ProducesProblem(500));
    }

    public override async Task HandleAsync(PushMessageRequest req, CancellationToken ct)
    {
        try
        {
            var result = await _pushGateway.SendPushAsync(req);

            if (result.IsSuccess)
            {
                await SendOkAsync(new ApiResponse<PushSendResult>
                {
                    Success = true,
                    Message = "Push notification sent successfully",
                    Data = new PushSendResult
                    {
                        MessageId = result.MessageId ?? Guid.NewGuid().ToString(),
                        Provider = _pushGateway.GetCurrentProvider() ?? "Unknown",
                        Status = "Sent",
                        SentAt = DateTime.UtcNow,
                        DeviceToken = req.DeviceToken,
                        Title = req.Title,
                        Body = req.Body,
                        Platform = req.Platform,
                        QueueId = "",
                        CorrelationId = Guid.NewGuid().ToString()
                    },
                    RequestId = HttpContext.TraceIdentifier,
                    Timestamp = DateTime.UtcNow
                }, ct);
            }
            else
            {
                await SendAsync(new ApiResponse<PushSendResult>
                {
                    Success = false,
                    Message = result.ErrorMessage ?? "Failed to send push notification",
                    Data = new { Error = result.ErrorMessage },
                    RequestId = HttpContext.TraceIdentifier,
                    Timestamp = DateTime.UtcNow
                }, 500, ct);
            }
        }
        catch (Exception ex)
        {
            await SendAsync(new ApiResponse<PushSendResult>
            {
                Success = false,
                Message = "An error occurred while sending the push notification",
                Data = new { Error = ex.Message },
                RequestId = HttpContext.TraceIdentifier,
                Timestamp = DateTime.UtcNow
            }, 500, ct);
        }
    }
}
