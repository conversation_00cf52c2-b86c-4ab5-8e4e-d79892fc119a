using PluginContract.Enums;

namespace PluginContract.Attributes;

/// <summary>
/// Attribute to mark classes as notification plugins
/// </summary>
[AttributeUsage(AttributeTargets.Class, AllowMultiple = false)]
public sealed class NotificationPluginAttribute : Attribute
{
    public string Name { get; }
    public string Version { get; }
    public string Description { get; }
    public PluginType Type { get; }
    public string Author { get; }

    public NotificationPluginAttribute(
        string name, 
        string version, 
        string description, 
        PluginType type, 
        string author)
    {
        Name = name;
        Version = version;
        Description = description;
        Type = type;
        Author = author;
    }
}
