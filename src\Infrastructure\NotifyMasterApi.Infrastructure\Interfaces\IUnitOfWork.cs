namespace NotifyMasterApi.Infrastructure.Interfaces;

/// <summary>
/// Unit of Work pattern interface for managing database transactions.
/// Provides access to all repositories and transaction management.
/// </summary>
public interface IUnitOfWork : IDisposable
{
    /// <summary>
    /// Gets the notification repository.
    /// </summary>
    INotificationRepository Notifications { get; }
    
    /// <summary>
    /// Gets the metrics repository.
    /// </summary>
    IMetricsRepository Metrics { get; }
    
    /// <summary>
    /// Gets the error repository.
    /// </summary>
    IErrorRepository Errors { get; }
    
    /// <summary>
    /// Saves all changes made in this unit of work to the database.
    /// </summary>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>The number of state entries written to the database.</returns>
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Begins a new database transaction.
    /// </summary>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>The database transaction.</returns>
    Task BeginTransactionAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Commits the current database transaction.
    /// </summary>
    /// <param name="cancellationToken">Cancellation token.</param>
    Task CommitTransactionAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Rolls back the current database transaction.
    /// </summary>
    /// <param name="cancellationToken">Cancellation token.</param>
    Task RollbackTransactionAsync(CancellationToken cancellationToken = default);
}
