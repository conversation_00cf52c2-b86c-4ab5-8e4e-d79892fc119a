using Microsoft.AspNetCore.Mvc;
using NotificationContract.Enums;
using NotificationContract.Models;
using NotifyMasterApi.Gateways;
using NotifyMasterApi.Services;
using NotifyMasterApi.Events.Services;
using NotifyMasterApi.Events.Models;
using NotifyMasterApi.Documentation;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Net;
using Microsoft.AspNetCore.Authorization;

namespace NotifyMasterApi.Controllers;

#region Push Response Models

/// <summary>
/// Push notification send operation result.
/// </summary>
public class PushSendResult
{
    /// <summary>
    /// Unique message identifier
    /// </summary>
    [Description("Unique identifier for the sent push notification")]
    public string MessageId { get; set; } = "";

    /// <summary>
    /// Push platform used for sending
    /// </summary>
    [Description("Platform used (FCM, APNS, Web Push, etc.)")]
    public string Platform { get; set; } = "";

    /// <summary>
    /// Current status of the push notification
    /// </summary>
    [Description("Current delivery status")]
    public string Status { get; set; } = "";

    /// <summary>
    /// Timestamp when push was sent
    /// </summary>
    [Description("When the push notification was sent")]
    public DateTime SentAt { get; set; }

    /// <summary>
    /// Recipient device token
    /// </summary>
    [Description("Device token of the recipient")]
    public string To { get; set; } = "";

    /// <summary>
    /// Push notification title
    /// </summary>
    [Description("Title of the push notification")]
    public string Title { get; set; } = "";

    /// <summary>
    /// Push notification body
    /// </summary>
    [Description("Body content of the push notification")]
    public string Body { get; set; } = "";

    /// <summary>
    /// Queue ID for tracking
    /// </summary>
    [Description("Queue identifier for tracking the push")]
    public string QueueId { get; set; } = "";

    /// <summary>
    /// Correlation ID for request tracking
    /// </summary>
    [Description("Correlation identifier for request tracking")]
    public string CorrelationId { get; set; } = "";

    /// <summary>
    /// Whether the notification has rich media
    /// </summary>
    [Description("Whether the notification includes images or media")]
    public bool HasRichMedia { get; set; }

    /// <summary>
    /// Number of action buttons
    /// </summary>
    [Description("Number of action buttons in the notification")]
    public int ActionCount { get; set; }
}

/// <summary>
/// Bulk push notification operation result.
/// </summary>
public class BulkPushResult
{
    /// <summary>
    /// Total number of push notifications processed
    /// </summary>
    [Description("Total number of push notifications in the batch")]
    public int TotalPushes { get; set; }

    /// <summary>
    /// Number of successfully sent push notifications
    /// </summary>
    [Description("Number of push notifications sent successfully")]
    public int SuccessfulSends { get; set; }

    /// <summary>
    /// Number of failed push notification sends
    /// </summary>
    [Description("Number of push notifications that failed to send")]
    public int FailedSends { get; set; }

    /// <summary>
    /// Batch processing identifier
    /// </summary>
    [Description("Unique identifier for the bulk operation")]
    public string BatchId { get; set; } = "";

    /// <summary>
    /// Individual push notification results
    /// </summary>
    [Description("Results for each individual push notification")]
    public List<PushSendResult> Results { get; set; } = new();

    /// <summary>
    /// Processing duration
    /// </summary>
    [Description("Time taken to process the batch")]
    public TimeSpan ProcessingTime { get; set; }

    /// <summary>
    /// Platform breakdown
    /// </summary>
    [Description("Number of notifications sent per platform")]
    public Dictionary<string, int> PlatformBreakdown { get; set; } = new();
}

/// <summary>
/// Push notification platform information.
/// </summary>
public class PushPlatformInfo
{
    /// <summary>
    /// Platform unique key
    /// </summary>
    [Description("Unique identifier for the platform")]
    public string Key { get; set; } = "";

    /// <summary>
    /// Platform display name
    /// </summary>
    [Description("Human-readable platform name")]
    public string Name { get; set; } = "";

    /// <summary>
    /// Whether this platform is currently active
    /// </summary>
    [Description("Whether this platform is currently active")]
    public bool IsActive { get; set; }

    /// <summary>
    /// Platform configuration status
    /// </summary>
    [Description("Configuration status of the platform")]
    public string Status { get; set; } = "";

    /// <summary>
    /// Supported features
    /// </summary>
    [Description("List of features supported by this platform")]
    public List<string> Features { get; set; } = new();

    /// <summary>
    /// Rate limits for this platform
    /// </summary>
    [Description("Rate limiting information")]
    public Dictionary<string, object> RateLimits { get; set; } = new();

    /// <summary>
    /// Maximum payload size
    /// </summary>
    [Description("Maximum payload size in bytes")]
    public int MaxPayloadSize { get; set; }

    /// <summary>
    /// Supported rich media types
    /// </summary>
    [Description("Supported rich media content types")]
    public List<string> SupportedMediaTypes { get; set; } = new();
}

#endregion

/// <summary>
/// 📱 Push Notification Service
/// </summary>
/// <remarks>
/// **Multi-platform push notifications with rich media and advanced targeting.**
/// 
/// ### 🎯 Key Features
/// - **Multi-Platform Support**: iOS (APNS), Android (FCM), Web Push
/// - **Rich Media**: Images, videos, and interactive content
/// - **Action Buttons**: Custom actions and deep linking
/// - **Bulk Operations**: Send thousands of notifications efficiently
/// - **Real-time Analytics**: Open rates, click tracking, conversions
/// - **Smart Targeting**: Audience segmentation and personalization
/// - **Scheduled Delivery**: Time zone aware scheduling
/// - **A/B Testing**: Split testing for optimization
/// 
/// ### 📊 Platform Support
/// | Platform | Features | Max Payload | Rich Media |
/// |----------|----------|-------------|-------------|
/// | **iOS (APNS)** | Silent, critical, grouped | 4KB | Images, videos |
/// | **Android (FCM)** | Data, notification, topics | 4KB | Images, actions |
/// | **Web Push** | Actions, badges, icons | 4KB | Images, icons |
/// | **Windows** | Tiles, toasts, badges | 5KB | Images, adaptive |
/// 
/// ### 🔧 Authentication Required
/// Use one of the following methods:
/// - **JWT Token**: `Authorization: Bearer {token}`
/// - **API Key**: `X-API-Key: {your-api-key}`
/// - **Basic Auth**: `Authorization: Basic {base64-credentials}`
/// 
/// ### 📈 Performance
/// - **Average Response Time**: < 100ms for single push
/// - **Bulk Processing**: Up to 10,000 notifications per request
/// - **Rate Limiting**: 5,000 requests per minute per user
/// - **Delivery Rate**: 99.9% for valid tokens
/// 
/// ### 🔔 Real-time Events
/// Subscribe to WebSocket events at `/events/push` for:
/// - Push sent confirmations
/// - Delivery status updates
/// - Open/click tracking
/// - Error alerts and warnings
/// 
/// ### 📱 Device Token Format
/// - **iOS**: 64-character hexadecimal string
/// - **Android**: FCM registration token
/// - **Web**: Web Push subscription endpoint
/// </remarks>
[ApiController]
[Route("api/v1/push")]
[Produces("application/json")]
[Tags("📱 Push Notifications")]
[Authorize]
public class PushController : ControllerBase
{
    private readonly IPushGateway _pushGateway;
    private readonly INotificationLoggingService _loggingService;
    private readonly INotificationQueueService _queueService;
    private readonly IEventPublisher _eventPublisher;
    private readonly ILogger<PushController> _logger;

    public PushController(
        IPushGateway pushGateway,
        INotificationLoggingService loggingService,
        INotificationQueueService queueService,
        IEventPublisher eventPublisher,
        ILogger<PushController> logger)
    {
        _pushGateway = pushGateway;
        _loggingService = loggingService;
        _queueService = queueService;
        _eventPublisher = eventPublisher;
        _logger = logger;
    }

    /// <summary>
    /// 📤 Send Push Notification
    /// </summary>
    /// <remarks>
    /// **Send a single push notification with rich media and action support.**
    /// 
    /// ### 🎯 Features
    /// - **Rich Media**: Images, videos, and interactive content
    /// - **Action Buttons**: Custom actions with deep linking
    /// - **Platform Optimization**: Automatic platform-specific formatting
    /// - **Silent Notifications**: Background updates without user interaction
    /// - **Critical Alerts**: High-priority notifications that bypass Do Not Disturb
    /// - **Queue Management**: Reliable delivery with retry logic
    /// 
    /// ### 📝 Request Examples
    /// 
    /// **Simple Push:**
    /// ```json
    /// {
    ///   "deviceToken": "fcm_token_here",
    ///   "title": "New Message",
    ///   "body": "You have a new message from John Doe",
    ///   "platform": "android"
    /// }
    /// ```
    /// 
    /// **Rich Media Push:**
    /// ```json
    /// {
    ///   "deviceToken": "apns_token_here",
    ///   "title": "Photo Shared! 📸",
    ///   "body": "Sarah shared a new photo with you",
    ///   "platform": "ios",
    ///   "imageUrl": "https://example.com/photo.jpg",
    ///   "actions": [
    ///     {"title": "View", "action": "view_photo"},
    ///     {"title": "Like", "action": "like_photo"}
    ///   ]
    /// }
    /// ```
    /// 
    /// ### ✅ Success Response
    /// ```json
    /// {
    ///   "success": true,
    ///   "message": "Push notification queued successfully",
    ///   "data": {
    ///     "messageId": "push_12345",
    ///     "queueId": "queue_67890",
    ///     "correlationId": "corr_abcdef"
    ///   }
    /// }
    /// ```
    /// 
    /// ### 🔔 Real-time Events
    /// Listen to `/events/push` WebSocket for delivery updates:
    /// - `push.queued` - Push added to queue
    /// - `push.sent` - Push successfully sent
    /// - `push.delivered` - Push delivered to device
    /// - `push.opened` - User opened the notification
    /// - `push.clicked` - User clicked an action
    /// - `push.failed` - Delivery failed
    /// </remarks>
    /// <param name="message">Push notification request with device token and content</param>
    /// <returns>Push notification sending result with message ID and queue information</returns>
    /// <response code="200">✅ Push notification queued successfully</response>
    /// <response code="400">❌ Invalid request data</response>
    /// <response code="401">🔒 Authentication required</response>
    /// <response code="422">⚠️ Validation failed</response>
    /// <response code="429">🚫 Rate limit exceeded</response>
    /// <response code="500">💥 Internal server error</response>
    [HttpPost("send")]
    [ProducesResponseType(typeof(ApiResponse<PushSendResult>), 200)]
    [ProducesResponseType(typeof(ApiResponse<object>), 400)]
    [ProducesResponseType(typeof(ApiResponse<object>), 401)]
    [ProducesResponseType(typeof(ValidationProblemDetails), 422)]
    [ProducesResponseType(typeof(ApiResponse<object>), 429)]
    [ProducesResponseType(typeof(ApiResponse<object>), 500)]
    public async Task<ActionResult> SendPush([FromBody, Required] PushMessageRequest message)
    {
        try
        {
            var correlationId = Guid.NewGuid().ToString();

            // Log the notification
            var messageId = await _loggingService.LogNotificationAsync(
                NotificationType.Push,
                message.DeviceToken,
                message.Title,
                message.Body,
                null, // UserId not available in PushMessageRequest
                correlationId);

            // Queue the notification
            var queueItem = new NotificationQueueItem
            {
                MessageId = messageId,
                Type = NotificationType.Push,
                Recipient = message.DeviceToken,
                Subject = message.Title,
                Content = message.Body,
                UserId = null, // UserId not available in PushMessageRequest
                CorrelationId = correlationId,
                Priority = NotificationPriority.Normal
            };

            var queueId = await _queueService.QueueNotificationAsync(queueItem);

            // Publish notification queued event
            var queuedEvent = new NotificationQueuedEvent
            {
                NotificationType = NotificationType.Push,
                MessageId = messageId,
                CorrelationId = correlationId,
                QueueId = queueId,
                Recipient = message.DeviceToken,
                Subject = message.Title,
                Priority = (int)NotificationPriority.Normal
            };

            await _eventPublisher.PublishNotificationEventAsync(queuedEvent);
            await _eventPublisher.PublishNotificationEventToTypeAsync("Push", queuedEvent);
            if (!string.IsNullOrEmpty(correlationId))
            {
                await _eventPublisher.PublishNotificationEventToCorrelationAsync(correlationId, queuedEvent);
            }

            _logger.LogInformation("Queued push notification {MessageId} with queue ID {QueueId}", messageId, queueId);

            return Ok(new ApiResponse<PushSendResult>
            {
                Success = true,
                Message = "Push notification queued successfully",
                Data = new PushSendResult
                {
                    MessageId = messageId,
                    Platform = message.Platform ?? "Unknown",
                    Status = "Queued",
                    SentAt = DateTime.UtcNow,
                    To = message.DeviceToken,
                    Title = message.Title,
                    Body = message.Body,
                    QueueId = queueId,
                    CorrelationId = correlationId,
                    HasRichMedia = !string.IsNullOrEmpty(message.ImageUrl),
                    ActionCount = message.Actions?.Count ?? 0
                },
                RequestId = HttpContext.TraceIdentifier,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing push notification request");
            return StatusCode(500, new ApiResponse<object>
            {
                Success = false,
                Message = "Internal server error",
                Data = new { Error = ex.Message },
                RequestId = HttpContext.TraceIdentifier,
                Timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// 🚀 Send Push Notification Directly (Bypass Queue)
    /// </summary>
    /// <remarks>
    /// **Send push notification immediately without queuing for urgent alerts.**
    ///
    /// ### ⚠️ Important Notes
    /// - **No Retry Logic**: Failed sends are not retried
    /// - **Blocking Operation**: Waits for platform response
    /// - **Rate Limits Apply**: Subject to platform rate limits
    /// - **Use Sparingly**: Recommended for urgent notifications only
    ///
    /// ### 🎯 Use Cases
    /// - Breaking news alerts
    /// - Security notifications
    /// - Time-sensitive offers
    /// - Emergency alerts
    ///
    /// ### 📝 Request Example
    /// ```json
    /// {
    ///   "deviceToken": "fcm_token_here",
    ///   "title": "🚨 Security Alert",
    ///   "body": "Suspicious login detected from new device",
    ///   "platform": "android",
    ///   "priority": "high"
    /// }
    /// ```
    /// </remarks>
    /// <param name="message">Push notification message to send directly</param>
    /// <returns>Direct push notification sending result</returns>
    /// <response code="200">✅ Push notification sent successfully</response>
    /// <response code="400">❌ Invalid request data</response>
    /// <response code="401">🔒 Authentication required</response>
    /// <response code="422">⚠️ Validation failed</response>
    /// <response code="429">🚫 Rate limit exceeded</response>
    /// <response code="500">💥 Push notification sending failed</response>
    [HttpPost("send/direct")]
    [ProducesResponseType(typeof(ApiResponse<PushSendResult>), 200)]
    [ProducesResponseType(typeof(ApiResponse<object>), 400)]
    [ProducesResponseType(typeof(ApiResponse<object>), 401)]
    [ProducesResponseType(typeof(ValidationProblemDetails), 422)]
    [ProducesResponseType(typeof(ApiResponse<object>), 429)]
    [ProducesResponseType(typeof(ApiResponse<object>), 500)]
    [ApiExample("""
    {
      "deviceToken": "fcm_token_12345",
      "title": "🚨 Security Alert",
      "body": "Suspicious login detected from IP ************* on device iPhone 15. If this wasn't you, secure your account immediately.",
      "platform": "android",
      "priority": "high",
      "imageUrl": "https://example.com/security-alert.png",
      "actions": [
        {"title": "Secure Account", "action": "secure_account"},
        {"title": "Not Me", "action": "report_suspicious"}
      ]
    }
    """, "Send urgent security alert push notification")]
    [ApiResponseExample(200, """
    {
      "success": true,
      "message": "Push notification sent successfully",
      "data": {
        "messageId": "push_urgent_12345",
        "platform": "FCM",
        "status": "Sent",
        "sentAt": "2024-01-01T12:00:00Z",
        "to": "fcm_token_12345",
        "title": "🚨 Security Alert",
        "body": "Suspicious login detected from IP *************...",
        "queueId": "",
        "correlationId": "corr_urgent_abcdef",
        "hasRichMedia": true,
        "actionCount": 2
      },
      "requestId": "req_urgent_98765",
      "timestamp": "2024-01-01T12:00:00Z"
    }
    """, "Direct push notification send response")]
    [ApiPerformance("< 1000ms", "1,000 requests/minute")]
    public async Task<ActionResult> SendPushDirect([FromBody, Required] PushMessageRequest message)
    {
        try
        {
            var result = await _pushGateway.SendPushAsync(message);

            if (result.IsSuccess)
            {
                return Ok(new ApiResponse<PushSendResult>
                {
                    Success = true,
                    Message = "Push notification sent successfully",
                    Data = new PushSendResult
                    {
                        MessageId = result.MessageId ?? Guid.NewGuid().ToString(),
                        Platform = message.Platform ?? "Unknown",
                        Status = "Sent",
                        SentAt = DateTime.UtcNow,
                        To = message.DeviceToken,
                        Title = message.Title,
                        Body = message.Body,
                        QueueId = "",
                        CorrelationId = Guid.NewGuid().ToString(),
                        HasRichMedia = !string.IsNullOrEmpty(message.ImageUrl),
                        ActionCount = message.Actions?.Count ?? 0
                    },
                    RequestId = HttpContext.TraceIdentifier,
                    Timestamp = DateTime.UtcNow
                });
            }

            return BadRequest(new ApiResponse<object>
            {
                Success = false,
                Message = "Push notification sending failed",
                Data = new { Error = result.ErrorMessage },
                RequestId = HttpContext.TraceIdentifier,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending push notification directly");
            return StatusCode(500, new ApiResponse<object>
            {
                Success = false,
                Message = "Internal server error",
                Data = new { Error = ex.Message },
                RequestId = HttpContext.TraceIdentifier,
                Timestamp = DateTime.UtcNow
            });
        }
    }
}
