using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace PluginCore.Models;

/// <summary>
/// Represents a plugin manifest containing metadata and configuration for a notification service plugin.
/// This manifest is embedded within plugin assemblies and defines how the plugin integrates with the system.
/// </summary>
/// <remarks>
/// The manifest supports various plugin types including Email, SMS, and Push notification providers.
/// Each plugin follows the naming convention: Plugin.{Type}.{Provider} (e.g., Plugin.Email.SendGrid).
/// </remarks>
public sealed class PluginManifest
{
    /// <summary>
    /// Gets or sets the unique name of the plugin.
    /// </summary>
    /// <example>Plugin.Email.SendGrid</example>
    [JsonPropertyName("name")]
    [JsonRequired]
    [Required(ErrorMessage = "Plugin name is required")]
    [StringLength(100, MinimumLength = 3, ErrorMessage = "Plugin name must be between 3 and 100 characters")]
    [RegularExpression(@"^Plugin\.[A-Za-z]+\.[A-Za-z]+$", ErrorMessage = "Plugin name must follow the pattern 'Plugin.{Type}.{Provider}'")]
    public required string Name { get; init; }

    /// <summary>
    /// Gets or sets the semantic version of the plugin.
    /// </summary>
    /// <example>1.0.0</example>
    [JsonPropertyName("version")]
    [JsonRequired]
    [Required(ErrorMessage = "Plugin version is required")]
    [RegularExpression(@"^\d+\.\d+\.\d+(?:-[a-zA-Z0-9\-\.]+)?$", ErrorMessage = "Version must follow semantic versioning (e.g., 1.0.0 or 1.0.0-beta)")]
    public required string Version { get; init; }

    /// <summary>
    /// Gets or sets a human-readable description of the plugin's functionality.
    /// </summary>
    [JsonPropertyName("description")]
    [StringLength(500, ErrorMessage = "Description cannot exceed 500 characters")]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the author or organization that created the plugin.
    /// </summary>
    [JsonPropertyName("author")]
    [StringLength(100, ErrorMessage = "Author name cannot exceed 100 characters")]
    public string Author { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the type of notification service this plugin provides.
    /// </summary>
    /// <remarks>
    /// Supported types: Email, Sms, Push
    /// </remarks>
    [JsonPropertyName("type")]
    [JsonRequired]
    [Required(ErrorMessage = "Plugin type is required")]
    [RegularExpression(@"^(Email|Sms|Push)$", ErrorMessage = "Type must be one of: Email, Sms, Push")]
    public required string Type { get; init; }

    /// <summary>
    /// Gets or sets the specific provider implementation (e.g., SendGrid, Twilio, Clickatel).
    /// </summary>
    [JsonPropertyName("provider")]
    [JsonRequired]
    [Required(ErrorMessage = "Provider name is required")]
    [StringLength(50, MinimumLength = 2, ErrorMessage = "Provider name must be between 2 and 50 characters")]
    public required string Provider { get; init; }

    /// <summary>
    /// Gets or sets the name of the assembly file containing the plugin implementation.
    /// </summary>
    /// <example>Plugin.Email.SendGrid.dll</example>
    [JsonPropertyName("assemblyName")]
    [JsonRequired]
    [Required(ErrorMessage = "Assembly name is required")]
    [StringLength(200, MinimumLength = 1, ErrorMessage = "Assembly name cannot exceed 200 characters")]
    public required string AssemblyName { get; init; }

    /// <summary>
    /// Gets or sets the fully qualified name of the main class that implements the plugin interface.
    /// </summary>
    /// <example>Plugin.Email.SendGrid.SendGridEmailService</example>
    [JsonPropertyName("entryPoint")]
    [JsonRequired]
    [Required(ErrorMessage = "Entry point is required")]
    [StringLength(300, MinimumLength = 1, ErrorMessage = "Entry point cannot exceed 300 characters")]
    public required string EntryPoint { get; init; }

    /// <summary>
    /// Gets or sets the list of external dependencies required by this plugin.
    /// </summary>
    [JsonPropertyName("dependencies")]
    public List<PluginDependency> Dependencies { get; set; } = [];

    /// <summary>
    /// Gets or sets the configuration schema defining the settings this plugin requires.
    /// </summary>
    [JsonPropertyName("configuration")]
    public Dictionary<string, PluginConfigurationItem> Configuration { get; set; } = [];

    /// <summary>
    /// Gets or sets the list of optional features supported by this plugin.
    /// </summary>
    /// <example>["html", "attachments", "templates"]</example>
    [JsonPropertyName("supportedFeatures")]
    public List<string> SupportedFeatures { get; set; } = [];

    /// <summary>
    /// Gets or sets the minimum .NET framework version required to run this plugin.
    /// </summary>
    [JsonPropertyName("minimumFrameworkVersion")]
    [RegularExpression(@"^net\d+\.\d+$", ErrorMessage = "Framework version must be in format 'netX.Y' (e.g., net9.0)")]
    public string MinimumFrameworkVersion { get; set; } = "net9.0";

    /// <summary>
    /// Gets or sets whether this plugin is enabled and available for use.
    /// </summary>
    [JsonPropertyName("isEnabled")]
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// Gets or sets the priority of this plugin when multiple plugins of the same type are available.
    /// Lower numbers indicate higher priority.
    /// </summary>
    [JsonPropertyName("priority")]
    [Range(1, 1000, ErrorMessage = "Priority must be between 1 and 1000")]
    public int Priority { get; set; } = 100;

    /// <summary>
    /// Gets or sets additional metadata for the plugin as key-value pairs.
    /// </summary>
    [JsonPropertyName("metadata")]
    public Dictionary<string, object> Metadata { get; set; } = [];
}

/// <summary>
/// Represents a dependency required by a plugin.
/// </summary>
public sealed class PluginDependency
{
    /// <summary>
    /// Gets or sets the name of the dependency package.
    /// </summary>
    [JsonPropertyName("name")]
    [JsonRequired]
    [Required(ErrorMessage = "Dependency name is required")]
    [StringLength(100, MinimumLength = 1, ErrorMessage = "Dependency name must be between 1 and 100 characters")]
    public required string Name { get; init; }

    /// <summary>
    /// Gets or sets the version requirement for the dependency.
    /// </summary>
    [JsonPropertyName("version")]
    [JsonRequired]
    [Required(ErrorMessage = "Dependency version is required")]
    [StringLength(50, MinimumLength = 1, ErrorMessage = "Version cannot exceed 50 characters")]
    public required string Version { get; init; }

    /// <summary>
    /// Gets or sets whether this dependency is required for the plugin to function.
    /// </summary>
    [JsonPropertyName("isRequired")]
    public bool IsRequired { get; set; } = true;
}

/// <summary>
/// Represents a configuration item that can be set for a plugin.
/// </summary>
public sealed class PluginConfigurationItem
{
    /// <summary>
    /// Gets or sets the data type of this configuration item.
    /// </summary>
    /// <remarks>
    /// Supported types: string, int, bool, secret, decimal
    /// </remarks>
    [JsonPropertyName("type")]
    [JsonRequired]
    [Required(ErrorMessage = "Configuration item type is required")]
    [RegularExpression(@"^(string|int|bool|secret|decimal)$", ErrorMessage = "Type must be one of: string, int, bool, secret, decimal")]
    public required string Type { get; init; }

    /// <summary>
    /// Gets or sets a human-readable description of this configuration item.
    /// </summary>
    [JsonPropertyName("description")]
    [StringLength(500, ErrorMessage = "Description cannot exceed 500 characters")]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the default value for this configuration item.
    /// </summary>
    [JsonPropertyName("defaultValue")]
    public object? DefaultValue { get; set; }

    /// <summary>
    /// Gets or sets whether this configuration item is required for the plugin to function.
    /// </summary>
    [JsonPropertyName("isRequired")]
    public bool IsRequired { get; set; } = true;

    /// <summary>
    /// Gets or sets whether this configuration item contains sensitive information that should be encrypted.
    /// </summary>
    [JsonPropertyName("isSecret")]
    public bool IsSecret { get; set; } = false;

    /// <summary>
    /// Gets or sets validation rules for this configuration item.
    /// </summary>
    [JsonPropertyName("validationRules")]
    public List<string> ValidationRules { get; set; } = [];
}
