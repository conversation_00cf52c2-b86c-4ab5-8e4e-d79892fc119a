using EmailContract.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using PluginContract.Interfaces;
using PluginContract.Models;
using PluginCore.Interfaces;
using PluginCore.Models;
using SendGrid;
using SendGrid.Helpers.Mail;

namespace Plugin.Email.Sendgrid;

public class SendgridPlugin : PluginCore.Interfaces.IEmailPlugin
{
    private readonly ILogger<SendgridPlugin> _logger;
    private Dictionary<string, PluginConfigurationItem> _configuration = new();
    private ISendGridClient? _sendGridClient;
    private bool _isInitialized = false;

    public SendgridPlugin(ILogger<SendgridPlugin> logger)
    {
        _logger = logger;
    }

    public PluginInfo PluginInfo => new(
        Name: "SendGrid Email Plugin",
        Version: "1.0.0",
        Description: "SendGrid email service plugin",
        Type: PluginContract.Enums.PluginType.Email,
        Author: "NotificationService Team"
    );

    public int? MaxRecipientsPerEmail => 1000;
    public int? MaxBulkSize => 1000;
    public bool SupportsTracking => true;
    public bool SupportsResending => false;

    public async Task InitializeAsync(IConfiguration configuration)
    {
        try
        {
            var apiKey = configuration["SendGrid:ApiKey"];
            if (string.IsNullOrEmpty(apiKey))
            {
                _logger.LogError("SendGrid API Key is required");
                throw new InvalidOperationException("SendGrid API Key is required");
            }

            var fromEmail = configuration["SendGrid:FromEmail"];
            if (string.IsNullOrEmpty(fromEmail))
            {
                _logger.LogError("From email address is required");
                throw new InvalidOperationException("From email address is required");
            }

            _sendGridClient = new SendGridClient(apiKey);

            _isInitialized = true;
            _logger.LogInformation("SendGrid plugin initialized successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize SendGrid plugin");
            throw;
        }
    }

    public async Task<NotificationResponse> SendAsync(SendEmailRequest request, CancellationToken cancellationToken = default)
    {
        if (!_isInitialized || _sendGridClient == null)
            throw new InvalidOperationException("Plugin not initialized");

        try
        {
            var from = new EmailAddress("<EMAIL>");
            var to = new EmailAddress(request.ReceptorMail, request.ReceptorName);
            var subject = request.Subject;
            var plainTextContent = request.Body;
            var htmlContent = request.Body; // Assuming body can contain HTML

            var msg = MailHelper.CreateSingleEmail(from, to, subject, plainTextContent, htmlContent);



            // Configure tracking
            msg.SetClickTracking(true, true);
            msg.SetOpenTracking(true);

            var response = await _sendGridClient.SendEmailAsync(msg);

            if (response.IsSuccessStatusCode)
            {
                var messageId = response.Headers.GetValues("X-Message-Id").FirstOrDefault() ?? Guid.NewGuid().ToString();
                _logger.LogInformation("Email sent via SendGrid. MessageId: {MessageId}", messageId);
                return new EmailResponse(true, messageId, null, new Dictionary<string, object> { ["Provider"] = "SendGrid" });
            }
            else
            {
                var errorBody = await response.Body.ReadAsStringAsync();
                _logger.LogError("SendGrid API error: {StatusCode} - {Error}", response.StatusCode, errorBody);
                return new EmailResponse(false, null, $"SendGrid API Error: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending email via SendGrid");
            return new EmailResponse(false, null, ex.Message);
        }
    }

    public async Task<NotificationResponse> SendBulkAsync(IEnumerable<SendEmailRequest> requests, CancellationToken cancellationToken = default)
    {
        var results = new List<NotificationResponse>();

        foreach (var request in requests)
        {
            var result = await SendAsync(request, cancellationToken);
            results.Add(result);
        }

        var successCount = results.Count(r => r.IsSuccess);
        return new EmailResponse(
            successCount == results.Count,
            null,
            successCount == results.Count ? null : $"{results.Count - successCount} messages failed",
            new Dictionary<string, object>
            {
                ["SuccessCount"] = successCount,
                ["FailureCount"] = results.Count - successCount,
                ["Results"] = results
            }
        );
    }

    public async Task<NotificationResponse> GetMessageStatusAsync(string messageId, CancellationToken cancellationToken = default)
    {
        // SendGrid doesn't provide direct message status API without webhooks
        return new EmailResponse(false, null, "Message status requires webhook configuration");
    }

    public async Task<NotificationResponse> GetMessageHistoryAsync(string messageId, int? limit = null, string? cursor = null, CancellationToken cancellationToken = default)
    {
        // SendGrid doesn't provide message history API
        return new EmailResponse(false, null, "Message history not available via SendGrid API");
    }

    public async Task<NotificationResponse> ResendMessageAsync(string messageId, CancellationToken cancellationToken = default)
    {
        // SendGrid doesn't support direct message resending
        return new EmailResponse(false, null, "Message resending not supported by SendGrid");
    }

    public async Task<bool> ValidateConfigurationAsync(CancellationToken cancellationToken = default)
    {
        if (!_isInitialized || _sendGridClient == null)
            return false;

        try
        {
            // Test the API key by making a simple API call
            // This is a basic validation - in production you might want to send a test email
            return true;
        }
        catch
        {
            return false;
        }
    }

    public void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        // No additional services needed for SendGrid
    }

    public async Task<bool> ValidateConfigurationAsync(IConfiguration configuration)
    {
        var apiKey = configuration["SendGrid:ApiKey"];
        var fromEmail = configuration["SendGrid:FromEmail"];

        return !string.IsNullOrEmpty(apiKey) && !string.IsNullOrEmpty(fromEmail);
    }

    public async Task<NotificationResponse> SendAsync(NotificationRequest request, CancellationToken cancellationToken = default)
    {
        // This method should not be called for email plugins since we have a specific SendEmailRequest
        return new EmailResponse(false, null, "Use SendAsync(SendEmailRequest) for email plugins");
    }

    public async Task<bool> HealthCheckAsync()
    {
        if (!_isInitialized || _sendGridClient == null)
        {
            return false;
        }

        try
        {
            // Basic health check - verify client is available
            return true;
        }
        catch
        {
            return false;
        }
    }



    public void Dispose()
    {
        // SendGrid client doesn't require explicit disposal
    }
}
