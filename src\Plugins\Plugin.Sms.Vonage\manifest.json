{"name": "Vonage SMS Provider", "description": "Vonage (formerly Nexmo) SMS provider plugin with global coverage and advanced features", "version": "1.0.0", "author": "NotifyMaster Team", "type": "SMS", "provider": "Vonage", "assemblyName": "Plugin.Sms.Vonage.dll", "className": "Plugin.Sms.Vonage.VonagePlugin", "supportedFeatures": ["SMS", "Unicode", "DeliveryReceipts", "LongMessages", "BulkSending", "GlobalCoverage", "TwoWayMessaging", "NumberInsight", "Verify"], "configuration": {"apiKey": {"type": "string", "required": true, "description": "Vonage API Key", "sensitive": false}, "apiSecret": {"type": "string", "required": true, "description": "Vonage API Secret", "sensitive": true}, "applicationId": {"type": "string", "required": false, "description": "Vonage Application ID (for advanced features)", "sensitive": false}, "privateKey": {"type": "string", "required": false, "description": "Private Key for JWT authentication (for advanced features)", "sensitive": true}, "webhookUrl": {"type": "string", "required": false, "description": "Webhook URL for delivery receipts", "sensitive": false}, "defaultFrom": {"type": "string", "required": false, "description": "Default sender ID or phone number", "sensitive": false, "default": "NotifyMaster"}, "enableDeliveryReceipts": {"type": "boolean", "required": false, "description": "Enable delivery receipt tracking", "default": true}, "messageType": {"type": "string", "required": false, "description": "Message type (text, unicode, binary)", "default": "text", "allowedValues": ["text", "unicode", "binary"]}, "ttl": {"type": "integer", "required": false, "description": "Message time-to-live in milliseconds", "default": 86400000}}, "dependencies": [{"name": "Vonage", "version": "7.0.1", "type": "NuGet"}], "capabilities": {"maxMessageLength": 1600, "supportsConcatenation": true, "supportsUnicode": true, "supportsDeliveryReceipts": true, "supportsBulkSending": true, "rateLimit": {"requestsPerSecond": 10, "burstLimit": 50}, "globalCoverage": true, "supportedCountries": ["*"], "pricing": {"model": "per-message", "currency": "USD", "basePrice": 0.0045}}, "metadata": {"website": "https://www.vonage.com/communications-apis/sms/", "documentation": "https://developer.vonage.com/messaging/sms/overview", "support": "https://developer.vonage.com/support", "tags": ["sms", "messaging", "global", "enterprise", "vonage", "nexmo"], "category": "Communication", "license": "MIT"}}