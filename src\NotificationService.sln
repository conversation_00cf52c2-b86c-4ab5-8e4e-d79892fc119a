﻿Microsoft Visual Studio Solution File, Format Version 12.00
# -------------------------------------------
# Removed duplicate project entries for "Contracts"
# -------------------------------------------

Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Contracts", "Contracts", "{95B6255C-B93D-478E-AD3D-A191BCE237FF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SmsContract", "Contracts\SmsContract\SmsContract.csproj", "{97D79402-837D-4092-A631-0C4EBFC615E1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EmailContract", "Contracts\EmailContract\EmailContract.csproj", "{F405E13B-CD9E-4739-A212-E7532CC8F8F3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PushNotificationContract", "Contracts\PushNotificationContract\PushNotificationContract.csproj", "{858F4302-ECAC-4E3F-873F-E53C8D8D727A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "NotificationContract", "Contracts\NotificationContract\NotificationContract.csproj", "{0419E9B3-97CF-4760-8866-F875239772C3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PushNotificationContract.Tests", "..\Tests\Contracts\PushNotificationContract.Tests\PushNotificationContract.Tests.csproj", "{637B3661-A006-4F7D-9672-58ACC0C4D38D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "NotificationContract.Tests", "..\Tests\Contracts\NotificationContract.Tests\NotificationContract.Tests.csproj", "{97646BBE-A716-43DB-8D6F-DE8131846BBE}"
EndProject

# Core and Infrastructure Projects
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Core", "Core", "{FBF56CC3-7AE6-AD2D-3F14-7F97FD322CD6}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Infrastructure", "Infrastructure", "{F1E2D3C4-B5A6-9780-1234-567890ABCDEF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "NotifyMasterApi", "Services\NotifyMasterApi\NotifyMasterApi.csproj", "{66479D6C-DC49-4484-90DB-77DF61FD9307}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "NotifyMasterApi.Infrastructure", "Infrastructure\NotifyMasterApi.Infrastructure\NotifyMasterApi.Infrastructure.csproj", "{A1B2C3D4-E5F6-7890-1234-567890ABCDEF}"
EndProject

# Libraries Projects
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EmailService.Library", "Libraries\EmailService.Library\EmailService.Library.csproj", "{7E950AA2-571F-F0FA-886A-BF546C105E14}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SmsService.Library", "Libraries\SmsService.Library\SmsService.Library.csproj", "{E19287B0-5D57-87EB-4918-713B6822B702}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PushNotificationService.Library", "Libraries\PushNotificationService.Library\PushNotificationService.Library.csproj", "{C63BE206-49F4-E1B2-6F39-03F37A90F7E7}"
EndProject

# Contract for Plugins
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PluginContract", "Contracts\PluginContract\PluginContract.csproj", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PluginCore", "Core\PluginCore\PluginCore.csproj", "{027968A0-A32C-BD51-37A1-6FFCBD3C2F46}"
EndProject

# Plugins (commented out, awaiting updates)
# Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Plugin.Email.Sendgrid", "Plugins\Plugin.Email.Sendgrid\Plugin.Email.Sendgrid.csproj", "{7E23781E-3B6D-4DFB-AE11-DA149B02CE40}"
# EndProject
# Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Plugin.Sms.BulkSms", "Plugins\Plugin.Sms.BulkSms\Plugin.Sms.BulkSms.csproj", "{EF1329CD-39A9-47DE-9E85-45E14CE49F99}"
# EndProject
# ...

# Solution configurations
Global
    GlobalSection(SolutionConfigurationPlatforms) = preSolution
        Debug|Any CPU = Debug|Any CPU
        Debug|x64 = Debug|x64
        Debug|x86 = Debug|x86
        Release|Any CPU = Release|Any CPU
        Release|x64 = Release|x64
        Release|x86 = Release|x86
    EndGlobalSection

    GlobalSection(ProjectConfigurationPlatforms) = postSolution
        # Examples for each project GUID
        # Replace with actual projects
        {97D79402-837D-4092-A631-0C4EBFC615E1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
        # ... (rest of the configurations for each project)
    EndGlobalSection

    GlobalSection(SolutionProperties) = preSolution
        HideSolutionNode = FALSE
    EndGlobalSection

    GlobalSection(NestedProjects) = preSolution
        # Define nested projects as needed
        # Example:
        # {95B6255C-B93D-478E-AD3D-A191BCE237FF} = {75E2ED49-09B4-4B89-9767-0AE9236050D7}
    EndGlobalSection
EndGlobal