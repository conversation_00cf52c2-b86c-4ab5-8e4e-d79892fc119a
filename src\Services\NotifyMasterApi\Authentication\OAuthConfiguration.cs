using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;

namespace NotifyMasterApi.Authentication;

/// <summary>
/// OAuth configuration options.
/// </summary>
public class OAuthOptions
{
    public const string SectionName = "OAuth";
    
    public bool Enabled { get; set; } = false;
    public string Authority { get; set; } = "";
    public string ClientId { get; set; } = "";
    public string ClientSecret { get; set; } = "";
    public string[] Scopes { get; set; } = Array.Empty<string>();
    public string CallbackPath { get; set; } = "/signin-oidc";
    public string SignedOutCallbackPath { get; set; } = "/signout-callback-oidc";
    public bool RequireHttpsMetadata { get; set; } = true;
    public bool SaveTokens { get; set; } = true;
    public string ResponseType { get; set; } = OpenIdConnectResponseType.Code;
    public Dictionary<string, string> AdditionalParameters { get; set; } = new();
}

/// <summary>
/// OAuth configuration service.
/// </summary>
public static class OAuthConfigurationExtensions
{
    /// <summary>
    /// Configures OAuth authentication.
    /// </summary>
    public static IServiceCollection AddOAuthAuthentication(this IServiceCollection services, IConfiguration configuration)
    {
        var oauthOptions = configuration.GetSection(OAuthOptions.SectionName).Get<OAuthOptions>();
        
        if (oauthOptions?.Enabled == true)
        {
            services.AddAuthentication()
                .AddOpenIdConnect("OAuth", options =>
                {
                    options.Authority = oauthOptions.Authority;
                    options.ClientId = oauthOptions.ClientId;
                    options.ClientSecret = oauthOptions.ClientSecret;
                    options.CallbackPath = oauthOptions.CallbackPath;
                    options.SignedOutCallbackPath = oauthOptions.SignedOutCallbackPath;
                    options.RequireHttpsMetadata = oauthOptions.RequireHttpsMetadata;
                    options.SaveTokens = oauthOptions.SaveTokens;
                    options.ResponseType = oauthOptions.ResponseType;

                    // Add scopes
                    options.Scope.Clear();
                    foreach (var scope in oauthOptions.Scopes)
                    {
                        options.Scope.Add(scope);
                    }

                    // Add additional parameters
                    foreach (var param in oauthOptions.AdditionalParameters)
                    {
                        options.Events.OnRedirectToIdentityProvider = context =>
                        {
                            context.ProtocolMessage.SetParameter(param.Key, param.Value);
                            return Task.CompletedTask;
                        };
                    }

                    // Handle token validation
                    options.Events.OnTokenValidated = async context =>
                    {
                        var userService = context.HttpContext.RequestServices.GetRequiredService<IUserService>();
                        var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<OAuthOptions>>();

                        try
                        {
                            var username = context.Principal?.Identity?.Name;
                            var email = context.Principal?.FindFirst("email")?.Value ?? "";
                            
                            if (!string.IsNullOrEmpty(username))
                            {
                                // Try to get existing user or create new one
                                var user = await userService.GetUserByUsernameAsync(username);
                                if (user == null)
                                {
                                    // Create new user with default permissions
                                    user = await userService.CreateUserAsync(
                                        username, 
                                        email, 
                                        Guid.NewGuid().ToString(), // Random password since OAuth user
                                        new[] { "User" }, 
                                        new[] { "read" }
                                    );
                                    
                                    logger.LogInformation("Created new OAuth user: {Username}", username);
                                }
                                else
                                {
                                    // Update last login
                                    user.LastLoginAt = DateTime.UtcNow;
                                    await userService.UpdateUserAsync(user);
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            logger.LogError(ex, "Error processing OAuth token validation");
                        }
                    };

                    options.Events.OnAuthenticationFailed = context =>
                    {
                        var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<OAuthOptions>>();
                        logger.LogError(context.Exception, "OAuth authentication failed");
                        return Task.CompletedTask;
                    };
                });
        }

        return services;
    }
}

/// <summary>
/// OAuth provider configurations for common providers.
/// </summary>
public static class OAuthProviders
{
    public static class Google
    {
        public const string Authority = "https://accounts.google.com";
        public static readonly string[] DefaultScopes = { "openid", "profile", "email" };
    }

    public static class Microsoft
    {
        public const string Authority = "https://login.microsoftonline.com/common/v2.0";
        public static readonly string[] DefaultScopes = { "openid", "profile", "email" };
    }

    public static class GitHub
    {
        public const string Authority = "https://github.com/login/oauth";
        public static readonly string[] DefaultScopes = { "user:email" };
    }

    public static class Auth0
    {
        public static string GetAuthority(string domain) => $"https://{domain}";
        public static readonly string[] DefaultScopes = { "openid", "profile", "email" };
    }
}

/// <summary>
/// OAuth configuration helper for appsettings.json.
/// </summary>
public class OAuthConfigurationHelper
{
    /// <summary>
    /// Gets sample OAuth configuration for appsettings.json.
    /// </summary>
    public static object GetSampleConfiguration()
    {
        return new
        {
            OAuth = new
            {
                Enabled = false,
                Authority = "https://your-oauth-provider.com",
                ClientId = "your-client-id",
                ClientSecret = "your-client-secret",
                Scopes = new[] { "openid", "profile", "email" },
                CallbackPath = "/signin-oidc",
                SignedOutCallbackPath = "/signout-callback-oidc",
                RequireHttpsMetadata = true,
                SaveTokens = true,
                ResponseType = "code",
                AdditionalParameters = new Dictionary<string, string>
                {
                    // Add any additional OAuth parameters here
                }
            }
        };
    }
}
