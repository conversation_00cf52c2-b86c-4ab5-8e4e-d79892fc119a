using System.Reflection;
using System.Runtime.Loader;

namespace NotifyMasterApi.Services;

/// <summary>
/// Custom AssemblyLoadContext for loading plugins in isolation
/// Allows for proper unloading of plugin assemblies
/// </summary>
public class PluginLoadContext : AssemblyLoadContext
{
    private readonly AssemblyDependencyResolver _resolver;
    private readonly string _pluginPath;

    public PluginLoadContext(string pluginPath, bool isCollectible = true) 
        : base($"PluginContext_{Path.GetFileNameWithoutExtension(pluginPath)}", isCollectible)
    {
        _pluginPath = pluginPath;
        _resolver = new AssemblyDependencyResolver(pluginPath);
    }

    protected override Assembly? Load(AssemblyName assemblyName)
    {
        // Try to resolve the assembly path
        string? assemblyPath = _resolver.ResolveAssemblyToPath(assemblyName);
        if (assemblyPath != null)
        {
            return LoadFromAssemblyPath(assemblyPath);
        }

        // Let the default context handle framework assemblies
        return null;
    }

    protected override IntPtr LoadUnmanagedDll(string unmanagedDllName)
    {
        string? libraryPath = _resolver.ResolveUnmanagedDllToPath(unmanagedDllName);
        if (libraryPath != null)
        {
            return LoadUnmanagedDllFromPath(libraryPath);
        }

        return IntPtr.Zero;
    }

    public Assembly LoadPluginAssembly()
    {
        return LoadFromAssemblyPath(_pluginPath);
    }
}
