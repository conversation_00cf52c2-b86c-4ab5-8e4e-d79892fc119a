namespace NotifyMasterApi.Events.Models;

/// <summary>
/// Base class for all administrative events
/// </summary>
public abstract class AdminEvent
{
    /// <summary>
    /// Unique identifier for the event
    /// </summary>
    public string EventId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// Timestamp when the event occurred
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// User or system that triggered the event
    /// </summary>
    public string? TriggeredBy { get; set; }

    /// <summary>
    /// Additional metadata for the event
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// Event fired when a plugin is loaded
/// </summary>
public class PluginLoadedEvent : AdminEvent
{
    /// <summary>
    /// Name of the loaded plugin
    /// </summary>
    public string PluginName { get; set; } = string.Empty;

    /// <summary>
    /// Version of the loaded plugin
    /// </summary>
    public string PluginVersion { get; set; } = string.Empty;

    /// <summary>
    /// Type of plugin (Email, SMS, Push)
    /// </summary>
    public string PluginType { get; set; } = string.Empty;

    /// <summary>
    /// Provider key for the plugin
    /// </summary>
    public string ProviderKey { get; set; } = string.Empty;

    /// <summary>
    /// Whether the plugin loaded successfully
    /// </summary>
    public bool LoadedSuccessfully { get; set; }

    /// <summary>
    /// Error message if loading failed
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// Event fired when a plugin is unloaded
/// </summary>
public class PluginUnloadedEvent : AdminEvent
{
    /// <summary>
    /// Name of the unloaded plugin
    /// </summary>
    public string PluginName { get; set; } = string.Empty;

    /// <summary>
    /// Provider key for the plugin
    /// </summary>
    public string ProviderKey { get; set; } = string.Empty;

    /// <summary>
    /// Reason for unloading
    /// </summary>
    public string UnloadReason { get; set; } = string.Empty;

    /// <summary>
    /// Whether the plugin was unloaded gracefully
    /// </summary>
    public bool GracefulUnload { get; set; }
}

/// <summary>
/// Event fired when a provider is switched
/// </summary>
public class ProviderSwitchedEvent : AdminEvent
{
    /// <summary>
    /// Type of service (Email, SMS, Push)
    /// </summary>
    public string ServiceType { get; set; } = string.Empty;

    /// <summary>
    /// Previous provider key
    /// </summary>
    public string? PreviousProvider { get; set; }

    /// <summary>
    /// New provider key
    /// </summary>
    public string NewProvider { get; set; } = string.Empty;

    /// <summary>
    /// Reason for the switch
    /// </summary>
    public string? SwitchReason { get; set; }
}

/// <summary>
/// Event fired when provider configuration is updated
/// </summary>
public class ProviderConfigUpdatedEvent : AdminEvent
{
    /// <summary>
    /// Provider key that was updated
    /// </summary>
    public string ProviderKey { get; set; } = string.Empty;

    /// <summary>
    /// Configuration keys that were changed
    /// </summary>
    public List<string> ChangedKeys { get; set; } = new();

    /// <summary>
    /// Whether the update was successful
    /// </summary>
    public bool UpdateSuccessful { get; set; }

    /// <summary>
    /// Error message if update failed
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// Event fired when system health status changes
/// </summary>
public class SystemHealthChangedEvent : AdminEvent
{
    /// <summary>
    /// Previous health status
    /// </summary>
    public string? PreviousStatus { get; set; }

    /// <summary>
    /// Current health status
    /// </summary>
    public string CurrentStatus { get; set; } = string.Empty;

    /// <summary>
    /// Component that caused the health change
    /// </summary>
    public string? AffectedComponent { get; set; }

    /// <summary>
    /// Health check details
    /// </summary>
    public Dictionary<string, object>? HealthDetails { get; set; }
}

/// <summary>
/// Event fired when metrics are updated
/// </summary>
public class MetricsUpdatedEvent : AdminEvent
{
    /// <summary>
    /// Type of metrics (Email, SMS, Push, System)
    /// </summary>
    public string MetricsType { get; set; } = string.Empty;

    /// <summary>
    /// Provider associated with the metrics
    /// </summary>
    public string? Provider { get; set; }

    /// <summary>
    /// Metrics data
    /// </summary>
    public Dictionary<string, object> MetricsData { get; set; } = new();

    /// <summary>
    /// Time period for the metrics
    /// </summary>
    public string TimePeriod { get; set; } = string.Empty;
}

/// <summary>
/// Event fired when an error occurs in the system
/// </summary>
public class SystemErrorEvent : AdminEvent
{
    /// <summary>
    /// Component where the error occurred
    /// </summary>
    public string Component { get; set; } = string.Empty;

    /// <summary>
    /// Error code
    /// </summary>
    public string ErrorCode { get; set; } = string.Empty;

    /// <summary>
    /// Error message
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// Severity level (1-5, where 5 is critical)
    /// </summary>
    public int Severity { get; set; } = 3;

    /// <summary>
    /// Stack trace if available
    /// </summary>
    public string? StackTrace { get; set; }

    /// <summary>
    /// Whether the error was handled gracefully
    /// </summary>
    public bool HandledGracefully { get; set; }
}

/// <summary>
/// Event fired when a test message is sent
/// </summary>
public class TestMessageEvent : AdminEvent
{
    /// <summary>
    /// Type of test message (Email, SMS, Push)
    /// </summary>
    public string MessageType { get; set; } = string.Empty;

    /// <summary>
    /// Provider used for the test
    /// </summary>
    public string Provider { get; set; } = string.Empty;

    /// <summary>
    /// Test recipient
    /// </summary>
    public string Recipient { get; set; } = string.Empty;

    /// <summary>
    /// Whether the test was successful
    /// </summary>
    public bool TestSuccessful { get; set; }

    /// <summary>
    /// Response time in milliseconds
    /// </summary>
    public double ResponseTimeMs { get; set; }

    /// <summary>
    /// Test result details
    /// </summary>
    public Dictionary<string, object>? TestResults { get; set; }
}
