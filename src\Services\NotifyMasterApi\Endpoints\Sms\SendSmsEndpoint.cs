using FastEndpoints;
using Microsoft.AspNetCore.Authorization;
using NotifyMasterApi.Gateways;
using NotifyMasterApi.Services;
using NotifyMasterApi.Events.Services;
using NotifyMasterApi.Documentation;
using SmsContract.Models;
using NotificationContract.Models;
using NotificationContract.Enums;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;

namespace NotifyMasterApi.Endpoints.Sms;

/// <summary>
/// Send SMS endpoint using FastEndpoints
/// </summary>
[HttpPost("/sms/send"), Authorize]
public class SendSmsEndpoint : Endpoint<SmsMessageRequest, ApiResponse<SmsSendResult>>
{
    private readonly ISmsGateway _smsGateway;
    private readonly INotificationLoggingService _loggingService;
    private readonly IEventPublisher _eventPublisher;

    public SendSmsEndpoint(
        ISmsGateway smsGateway,
        INotificationLoggingService loggingService,
        IEventPublisher eventPublisher)
    {
        _smsGateway = smsGateway;
        _loggingService = loggingService;
        _eventPublisher = eventPublisher;
    }

    public override void Configure()
    {
        Post("/sms/send");
        Policies("ApiKeyPolicy", "JwtPolicy");
        Summary(s =>
        {
            s.Summary = "📱 Send SMS (Queued)";
            s.Description = "Queue an SMS for delivery with automatic provider failover and retry logic";
            s.ExampleRequest = new SmsMessageRequest
            {
                PhoneNumber = "+**********",
                Message = "🎉 Welcome! Your account is ready. Enjoy using NotifyMaster! 🚀"
            };
        });
        
        Description(builder => builder
            .Accepts<SmsMessageRequest>("application/json")
            .Produces<ApiResponse<SmsSendResult>>(200, "application/json")
            .ProducesProblem(400)
            .ProducesProblem(401)
            .ProducesProblem(422)
            .ProducesProblem(429)
            .ProducesProblem(500));
    }

    public override async Task HandleAsync(SmsMessageRequest req, CancellationToken ct)
    {
        try
        {
            var correlationId = Guid.NewGuid().ToString();

            // Log the notification
            var messageId = await _loggingService.LogNotificationAsync(
                NotificationType.Sms,
                req.PhoneNumber,
                "SMS",
                req.Message,
                null, // UserId not available in SmsMessageRequest
                correlationId);

            // Send the SMS
            var result = await _smsGateway.SendSmsAsync(req);

            if (result.IsSuccess)
            {
                // Publish SMS sent event
                var smsEvent = new SmsSentEvent
                {
                    MessageId = result.MessageId ?? messageId,
                    To = req.PhoneNumber,
                    Message = req.Message,
                    Provider = _smsGateway.GetCurrentProvider() ?? "Unknown",
                    SentAt = DateTime.UtcNow,
                    CorrelationId = correlationId
                };

                await _eventPublisher.PublishAsync(smsEvent);

                await SendOkAsync(new ApiResponse<SmsSendResult>
                {
                    Success = true,
                    Message = "SMS queued successfully",
                    Data = new SmsSendResult
                    {
                        MessageId = result.MessageId ?? messageId,
                        Provider = _smsGateway.GetCurrentProvider() ?? "Unknown",
                        Status = "Queued",
                        SentAt = DateTime.UtcNow,
                        To = req.PhoneNumber,
                        Message = req.Message,
                        QueueId = result.QueueId ?? "",
                        CorrelationId = correlationId,
                        SegmentCount = CalculateSegmentCount(req.Message),
                        Cost = CalculateCost(req.Message)
                    },
                    RequestId = HttpContext.TraceIdentifier,
                    Timestamp = DateTime.UtcNow
                }, ct);
            }
            else
            {
                await SendAsync(new ApiResponse<SmsSendResult>
                {
                    Success = false,
                    Message = result.ErrorMessage ?? "Failed to send SMS",
                    Data = null,
                    RequestId = HttpContext.TraceIdentifier,
                    Timestamp = DateTime.UtcNow
                }, 500, ct);
            }
        }
        catch (Exception ex)
        {
            await SendAsync(new ApiResponse<SmsSendResult>
            {
                Success = false,
                Message = "An error occurred while processing the SMS",
                Data = null,
                RequestId = HttpContext.TraceIdentifier,
                Timestamp = DateTime.UtcNow
            }, 500, ct);
        }
    }

    private static int CalculateSegmentCount(string message)
    {
        // Basic SMS segment calculation (160 chars per segment for GSM 7-bit)
        return (int)Math.Ceiling(message.Length / 160.0);
    }

    private static decimal CalculateCost(string message)
    {
        // Basic cost calculation (example: $0.01 per segment)
        var segments = CalculateSegmentCount(message);
        return segments * 0.01m;
    }
}

/// <summary>
/// SMS sent event for WebSocket notifications
/// </summary>
public class SmsSentEvent
{
    public string MessageId { get; set; } = "";
    public string To { get; set; } = "";
    public string Message { get; set; } = "";
    public string Provider { get; set; } = "";
    public DateTime SentAt { get; set; }
    public string CorrelationId { get; set; } = "";
}

/// <summary>
/// SMS send result model
/// </summary>
public class SmsSendResult
{
    /// <summary>
    /// Unique message identifier
    /// </summary>
    [Description("Unique identifier for the SMS message")]
    public string MessageId { get; set; } = "";

    /// <summary>
    /// SMS provider used
    /// </summary>
    [Description("SMS service provider that handled the message")]
    public string Provider { get; set; } = "";

    /// <summary>
    /// Current status of the SMS
    /// </summary>
    [Description("Current delivery status of the SMS")]
    public string Status { get; set; } = "";

    /// <summary>
    /// Timestamp when SMS was sent
    /// </summary>
    [Description("UTC timestamp when the SMS was processed")]
    public DateTime SentAt { get; set; }

    /// <summary>
    /// Recipient phone number
    /// </summary>
    [Description("Phone number of the recipient")]
    public string To { get; set; } = "";

    /// <summary>
    /// SMS message content
    /// </summary>
    [Description("Content of the SMS message")]
    public string Message { get; set; } = "";

    /// <summary>
    /// Queue ID for tracking
    /// </summary>
    [Description("Queue identifier for tracking the SMS")]
    public string QueueId { get; set; } = "";

    /// <summary>
    /// Correlation ID for request tracking
    /// </summary>
    [Description("Correlation identifier for request tracking")]
    public string CorrelationId { get; set; } = "";

    /// <summary>
    /// Number of SMS segments
    /// </summary>
    [Description("Number of SMS segments required for the message")]
    public int SegmentCount { get; set; }

    /// <summary>
    /// Cost of sending the SMS
    /// </summary>
    [Description("Cost in USD for sending the SMS")]
    public decimal Cost { get; set; }
}
