namespace NotificationContract.Enums;
/// <summary>
///  Enum representing the status of a notification
/// </summary>
/// <remarks>
/// This enum represents the different statuses a notification can have, including:
/// - Pending: The notification is waiting in the queue to be processed
/// - Processing: The notification is currently being processed
/// - Sent: The notification has been sent to the recipient
/// - Delivered: The notification has been delivered to the recipient
/// - Failed: The notification has failed to be sent
/// - Cancelled: The notification has been cancelled
/// </remarks>
public enum NotificationStatus
{
    Pending,
    Processing,
    Sent,
    Delivered,
    Failed,
    Cancelled
}
