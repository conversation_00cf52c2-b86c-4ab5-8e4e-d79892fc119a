using Microsoft.AspNetCore.SignalR;
using NotifyMasterApi.Events.Hubs;
using NotifyMasterApi.Events.Models;

namespace NotifyMasterApi.Events.Services;

/// <summary>
/// Service for publishing events to WebSocket clients via SignalR
/// </summary>
public class EventPublisher : IEventPublisher
{
    private readonly IHubContext<NotificationHub> _notificationHubContext;
    private readonly IHubContext<AdminHub> _adminHubContext;
    private readonly ILogger<EventPublisher> _logger;

    public EventPublisher(
        IHubContext<NotificationHub> notificationHubContext,
        IHubContext<AdminHub> adminHubContext,
        ILogger<EventPublisher> logger)
    {
        _notificationHubContext = notificationHubContext;
        _adminHubContext = adminHubContext;
        _logger = logger;
    }

    /// <inheritdoc />
    public async Task PublishNotificationEventAsync(NotificationEvent notificationEvent, CancellationToken cancellationToken = default)
    {
        try
        {
            await _notificationHubContext.Clients.Group("all_notifications")
                .SendAsync("NotificationEvent", notificationEvent, cancellationToken);

            _logger.LogDebug("Published notification event {EventType} with ID {EventId} to all clients",
                notificationEvent.GetType().Name, notificationEvent.EventId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing notification event {EventType} with ID {EventId}",
                notificationEvent.GetType().Name, notificationEvent.EventId);
        }
    }

    /// <inheritdoc />
    public async Task PublishAdminEventAsync(AdminEvent adminEvent, CancellationToken cancellationToken = default)
    {
        try
        {
            await _adminHubContext.Clients.Group("all_admin_events")
                .SendAsync("AdminEvent", adminEvent, cancellationToken);

            _logger.LogDebug("Published admin event {EventType} with ID {EventId} to admin clients",
                adminEvent.GetType().Name, adminEvent.EventId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing admin event {EventType} with ID {EventId}",
                adminEvent.GetType().Name, adminEvent.EventId);
        }
    }

    /// <inheritdoc />
    public async Task PublishNotificationEventToUserAsync(string userId, NotificationEvent notificationEvent, CancellationToken cancellationToken = default)
    {
        try
        {
            var groupName = $"user_{userId}";
            await _notificationHubContext.Clients.Group(groupName)
                .SendAsync("NotificationEvent", notificationEvent, cancellationToken);

            _logger.LogDebug("Published notification event {EventType} with ID {EventId} to user {UserId}",
                notificationEvent.GetType().Name, notificationEvent.EventId, userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing notification event {EventType} with ID {EventId} to user {UserId}",
                notificationEvent.GetType().Name, notificationEvent.EventId, userId);
        }
    }

    /// <inheritdoc />
    public async Task PublishNotificationEventToTypeAsync(string notificationType, NotificationEvent notificationEvent, CancellationToken cancellationToken = default)
    {
        try
        {
            var groupName = $"type_{notificationType.ToLower()}";
            await _notificationHubContext.Clients.Group(groupName)
                .SendAsync("NotificationEvent", notificationEvent, cancellationToken);

            _logger.LogDebug("Published notification event {EventType} with ID {EventId} to {NotificationType} subscribers",
                notificationEvent.GetType().Name, notificationEvent.EventId, notificationType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing notification event {EventType} with ID {EventId} to {NotificationType} subscribers",
                notificationEvent.GetType().Name, notificationEvent.EventId, notificationType);
        }
    }

    /// <inheritdoc />
    public async Task PublishNotificationEventToProviderAsync(string provider, NotificationEvent notificationEvent, CancellationToken cancellationToken = default)
    {
        try
        {
            var groupName = $"provider_{provider.ToLower()}";
            await _notificationHubContext.Clients.Group(groupName)
                .SendAsync("NotificationEvent", notificationEvent, cancellationToken);

            _logger.LogDebug("Published notification event {EventType} with ID {EventId} to {Provider} provider subscribers",
                notificationEvent.GetType().Name, notificationEvent.EventId, provider);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing notification event {EventType} with ID {EventId} to {Provider} provider subscribers",
                notificationEvent.GetType().Name, notificationEvent.EventId, provider);
        }
    }

    /// <inheritdoc />
    public async Task PublishNotificationEventToCorrelationAsync(string correlationId, NotificationEvent notificationEvent, CancellationToken cancellationToken = default)
    {
        try
        {
            var groupName = $"correlation_{correlationId}";
            await _notificationHubContext.Clients.Group(groupName)
                .SendAsync("NotificationEvent", notificationEvent, cancellationToken);

            _logger.LogDebug("Published notification event {EventType} with ID {EventId} to correlation {CorrelationId} subscribers",
                notificationEvent.GetType().Name, notificationEvent.EventId, correlationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing notification event {EventType} with ID {EventId} to correlation {CorrelationId} subscribers",
                notificationEvent.GetType().Name, notificationEvent.EventId, correlationId);
        }
    }

    /// <inheritdoc />
    public async Task PublishSystemHealthEventAsync(SystemHealthChangedEvent healthEvent, CancellationToken cancellationToken = default)
    {
        try
        {
            await _adminHubContext.Clients.Group("system_health")
                .SendAsync("SystemHealthEvent", healthEvent, cancellationToken);

            _logger.LogDebug("Published system health event with ID {EventId} - status changed to {Status}",
                healthEvent.EventId, healthEvent.CurrentStatus);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing system health event with ID {EventId}",
                healthEvent.EventId);
        }
    }

    /// <inheritdoc />
    public async Task PublishPluginEventAsync(AdminEvent pluginEvent, CancellationToken cancellationToken = default)
    {
        try
        {
            await _adminHubContext.Clients.Group("plugin_events")
                .SendAsync("PluginEvent", pluginEvent, cancellationToken);

            _logger.LogDebug("Published plugin event {EventType} with ID {EventId}",
                pluginEvent.GetType().Name, pluginEvent.EventId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing plugin event {EventType} with ID {EventId}",
                pluginEvent.GetType().Name, pluginEvent.EventId);
        }
    }

    /// <inheritdoc />
    public async Task PublishMetricsEventAsync(MetricsUpdatedEvent metricsEvent, CancellationToken cancellationToken = default)
    {
        try
        {
            await _adminHubContext.Clients.Group("metrics_updates")
                .SendAsync("MetricsEvent", metricsEvent, cancellationToken);

            _logger.LogDebug("Published metrics event with ID {EventId} for {MetricsType}",
                metricsEvent.EventId, metricsEvent.MetricsType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing metrics event with ID {EventId}",
                metricsEvent.EventId);
        }
    }

    /// <inheritdoc />
    public async Task PublishSystemErrorEventAsync(SystemErrorEvent errorEvent, CancellationToken cancellationToken = default)
    {
        try
        {
            await _adminHubContext.Clients.Group("system_errors")
                .SendAsync("SystemErrorEvent", errorEvent, cancellationToken);

            _logger.LogDebug("Published system error event with ID {EventId} from component {Component}",
                errorEvent.EventId, errorEvent.Component);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing system error event with ID {EventId}",
                errorEvent.EventId);
        }
    }
}
