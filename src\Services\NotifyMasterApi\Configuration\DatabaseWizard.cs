using Microsoft.Extensions.Configuration;
using System.Data.Common;
using System.Text.Json;
using Npgsql;
using Microsoft.Data.SqlClient;
using MySqlConnector;
using Microsoft.Data.Sqlite;

namespace NotifyMasterApi.Configuration;

/// <summary>
/// Interactive database configuration wizard for first-time setup.
/// </summary>
public class DatabaseWizard
{
    private readonly IConfiguration _configuration;
    private readonly string _appSettingsPath;

    public DatabaseWizard(IConfiguration configuration)
    {
        _configuration = configuration;
        _appSettingsPath = Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json");
    }

    /// <summary>
    /// Runs the database configuration wizard if no valid connection string is found.
    /// </summary>
    /// <returns>True if configuration was successful or already exists.</returns>
    public async Task<bool> RunWizardIfNeededAsync()
    {
        var existingConnection = _configuration.GetConnectionString("DefaultConnection");
        
        if (!string.IsNullOrEmpty(existingConnection) && await TestConnectionAsync(existingConnection))
        {
            Console.WriteLine("✅ Existing database connection is valid.");
            return true;
        }

        Console.WriteLine("🔧 Database Configuration Wizard");
        Console.WriteLine("================================");
        Console.WriteLine();

        if (!string.IsNullOrEmpty(existingConnection))
        {
            Console.WriteLine("⚠️  Existing connection string failed validation.");
        }
        else
        {
            Console.WriteLine("ℹ️  No database connection string found.");
        }

        Console.WriteLine("Let's configure your database connection...");
        Console.WriteLine();

        return await RunInteractiveWizardAsync();
    }

    /// <summary>
    /// Runs the interactive configuration wizard.
    /// </summary>
    private async Task<bool> RunInteractiveWizardAsync()
    {
        try
        {
            // Step 1: Database Provider Selection
            var provider = SelectDatabaseProvider();
            if (provider == null) return false;

            // Step 2: Connection Details
            if (provider == null) return false;
            var connectionString = await GetConnectionDetailsAsync(provider.Value);
            if (connectionString == null) return false;

            // Step 3: Test Connection
            if (!await TestConnectionWithRetryAsync(connectionString))
            {
                Console.WriteLine("❌ Unable to establish database connection.");
                Console.WriteLine("Would you like to continue with in-memory fallback? (y/n)");
                var fallback = Console.ReadLine()?.ToLower();
                return fallback == "y" || fallback == "yes";
            }

            // Step 4: Save Configuration
            await SaveConnectionStringAsync(connectionString);
            Console.WriteLine("✅ Database configuration saved successfully!");
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Configuration wizard failed: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Displays database provider selection menu.
    /// </summary>
    private DatabaseProvider? SelectDatabaseProvider()
    {
        Console.WriteLine("📋 Step 1: Select Database Provider");
        Console.WriteLine("===================================");
        Console.WriteLine("1. PostgreSQL (Recommended)");
        Console.WriteLine("2. SQL Server");
        Console.WriteLine("3. MySQL / MariaDB");
        Console.WriteLine("4. SQLite (File-based)");
        Console.WriteLine("5. Custom Connection String");
        Console.WriteLine("6. Skip (Use In-Memory Database)");
        Console.WriteLine();
        Console.Write("Select option (1-6): ");

        var input = Console.ReadLine();
        return input switch
        {
            "1" => DatabaseProvider.PostgreSQL,
            "2" => DatabaseProvider.SqlServer,
            "3" => DatabaseProvider.MySQL,
            "4" => DatabaseProvider.SQLite,
            "5" => DatabaseProvider.Custom,
            "6" => null,
            _ => SelectDatabaseProvider() // Retry on invalid input
        };
    }

    /// <summary>
    /// Gets connection details based on the selected provider.
    /// </summary>
    private async Task<string?> GetConnectionDetailsAsync(DatabaseProvider provider)
    {
        Console.WriteLine();
        Console.WriteLine($"📝 Step 2: Configure {provider} Connection");
        Console.WriteLine("==========================================");

        return provider switch
        {
            DatabaseProvider.PostgreSQL => GetPostgreSQLConnection(),
            DatabaseProvider.SqlServer => GetSqlServerConnection(),
            DatabaseProvider.MySQL => GetMySQLConnection(),
            DatabaseProvider.SQLite => await GetSQLiteConnectionAsync(),
            DatabaseProvider.Custom => GetCustomConnection(),
            _ => null
        };
    }

    /// <summary>
    /// Gets PostgreSQL connection details.
    /// </summary>
    private string GetPostgreSQLConnection()
    {
        Console.Write("Host (default: localhost): ");
        var host = Console.ReadLine();
        if (string.IsNullOrEmpty(host)) host = "localhost";

        Console.Write("Port (default: 5432): ");
        var portInput = Console.ReadLine();
        var port = string.IsNullOrEmpty(portInput) ? 5432 : int.Parse(portInput);

        Console.Write("Database name: ");
        var database = Console.ReadLine();

        Console.Write("Username: ");
        var username = Console.ReadLine();

        Console.Write("Password: ");
        var password = ReadPassword();

        Console.Write("Use SSL? (y/n, default: n): ");
        var sslInput = Console.ReadLine()?.ToLower();
        var sslMode = (sslInput == "y" || sslInput == "yes") ? "Require" : "Prefer";

        return $"Host={host};Port={port};Database={database};Username={username};Password={password};SSL Mode={sslMode}";
    }

    /// <summary>
    /// Gets SQL Server connection details.
    /// </summary>
    private string GetSqlServerConnection()
    {
        Console.Write("Server (default: localhost): ");
        var server = Console.ReadLine();
        if (string.IsNullOrEmpty(server)) server = "localhost";

        Console.Write("Database name: ");
        var database = Console.ReadLine();

        Console.Write("Use Windows Authentication? (y/n): ");
        var windowsAuth = Console.ReadLine()?.ToLower();

        if (windowsAuth == "y" || windowsAuth == "yes")
        {
            return $"Server={server};Database={database};Integrated Security=true;TrustServerCertificate=true";
        }
        else
        {
            Console.Write("Username: ");
            var username = Console.ReadLine();

            Console.Write("Password: ");
            var password = ReadPassword();

            return $"Server={server};Database={database};User Id={username};Password={password};TrustServerCertificate=true";
        }
    }

    /// <summary>
    /// Gets MySQL connection details.
    /// </summary>
    private string GetMySQLConnection()
    {
        Console.Write("Host (default: localhost): ");
        var host = Console.ReadLine();
        if (string.IsNullOrEmpty(host)) host = "localhost";

        Console.Write("Port (default: 3306): ");
        var portInput = Console.ReadLine();
        var port = string.IsNullOrEmpty(portInput) ? 3306 : int.Parse(portInput);

        Console.Write("Database name: ");
        var database = Console.ReadLine();

        Console.Write("Username: ");
        var username = Console.ReadLine();

        Console.Write("Password: ");
        var password = ReadPassword();

        return $"Server={host};Port={port};Database={database};Uid={username};Pwd={password};";
    }

    /// <summary>
    /// Gets SQLite connection details.
    /// </summary>
    private async Task<string> GetSQLiteConnectionAsync()
    {
        Console.WriteLine("SQLite Database Configuration:");
        Console.Write("Database file path (default: ./data/notifications.db): ");
        var filePath = Console.ReadLine();
        
        if (string.IsNullOrEmpty(filePath))
        {
            filePath = "./data/notifications.db";
        }

        // Ensure directory exists
        var directory = Path.GetDirectoryName(filePath);
        if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
        {
            Directory.CreateDirectory(directory);
            Console.WriteLine($"📁 Created directory: {directory}");
        }

        return $"Data Source={filePath}";
    }

    /// <summary>
    /// Gets custom connection string.
    /// </summary>
    private string GetCustomConnection()
    {
        Console.WriteLine("Enter your custom connection string:");
        Console.Write("Connection String: ");
        return Console.ReadLine() ?? "";
    }

    /// <summary>
    /// Reads password input with masking.
    /// </summary>
    private string ReadPassword()
    {
        var password = "";
        ConsoleKeyInfo key;

        do
        {
            key = Console.ReadKey(true);
            if (key.Key != ConsoleKey.Backspace && key.Key != ConsoleKey.Enter)
            {
                password += key.KeyChar;
                Console.Write("*");
            }
            else if (key.Key == ConsoleKey.Backspace && password.Length > 0)
            {
                password = password[0..^1];
                Console.Write("\b \b");
            }
        }
        while (key.Key != ConsoleKey.Enter);

        Console.WriteLine();
        return password;
    }

    /// <summary>
    /// Tests database connection with retry logic.
    /// </summary>
    private async Task<bool> TestConnectionWithRetryAsync(string connectionString)
    {
        Console.WriteLine();
        Console.WriteLine("🔍 Step 3: Testing Database Connection");
        Console.WriteLine("=====================================");

        for (int attempt = 1; attempt <= 3; attempt++)
        {
            Console.Write($"Attempt {attempt}/3... ");
            
            if (await TestConnectionAsync(connectionString))
            {
                Console.WriteLine("✅ Success!");
                return true;
            }
            
            Console.WriteLine("❌ Failed");
            
            if (attempt < 3)
            {
                Console.WriteLine("Retrying in 2 seconds...");
                await Task.Delay(2000);
            }
        }

        return false;
    }

    /// <summary>
    /// Tests a database connection string.
    /// </summary>
    public async Task<bool> TestConnectionAsync(string connectionString)
    {
        try
        {
            DbConnection connection = DetectProviderAndCreateConnection(connectionString);
            
            await connection.OpenAsync();
            await connection.CloseAsync();
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Connection test failed: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Detects database provider and creates appropriate connection.
    /// </summary>
    private DbConnection DetectProviderAndCreateConnection(string connectionString)
    {
        // Try to detect provider based on connection string format
        if (connectionString.Contains("Host=") || connectionString.Contains("Server=") && connectionString.Contains("Database="))
        {
            if (connectionString.Contains("Host="))
                return new NpgsqlConnection(connectionString);
            else
                return new SqlConnection(connectionString);
        }
        else if (connectionString.Contains("Data Source=") && connectionString.Contains(".db"))
        {
            return new SqliteConnection(connectionString);
        }
        else if (connectionString.Contains("Server=") && connectionString.Contains("Uid="))
        {
            return new MySqlConnection(connectionString);
        }
        
        // Default to PostgreSQL
        return new NpgsqlConnection(connectionString);
    }

    /// <summary>
    /// Saves the connection string to appsettings.json.
    /// </summary>
    public async Task SaveConnectionStringAsync(string connectionString)
    {
        Console.WriteLine();
        Console.WriteLine("💾 Step 4: Saving Configuration");
        Console.WriteLine("===============================");

        try
        {
            var json = await File.ReadAllTextAsync(_appSettingsPath);
            var config = JsonSerializer.Deserialize<Dictionary<string, object>>(json) ?? new();

            // Update connection strings
            if (!config.ContainsKey("ConnectionStrings"))
            {
                config["ConnectionStrings"] = new Dictionary<string, object>();
            }

            var connectionStrings = JsonSerializer.Deserialize<Dictionary<string, object>>(
                JsonSerializer.Serialize(config["ConnectionStrings"])) ?? new();
            
            connectionStrings["DefaultConnection"] = connectionString;
            config["ConnectionStrings"] = connectionStrings;

            // Write back to file
            var options = new JsonSerializerOptions { WriteIndented = true };
            var updatedJson = JsonSerializer.Serialize(config, options);
            await File.WriteAllTextAsync(_appSettingsPath, updatedJson);

            Console.WriteLine($"✅ Configuration saved to {_appSettingsPath}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"⚠️  Failed to save configuration: {ex.Message}");
            Console.WriteLine("Please manually add the following to your appsettings.json:");
            Console.WriteLine($"\"ConnectionStrings\": {{ \"DefaultConnection\": \"{connectionString}\" }}");
        }
    }
}

/// <summary>
/// Supported database providers.
/// </summary>
public enum DatabaseProvider
{
    PostgreSQL,
    SqlServer,
    MySQL,
    SQLite,
    Custom
}
