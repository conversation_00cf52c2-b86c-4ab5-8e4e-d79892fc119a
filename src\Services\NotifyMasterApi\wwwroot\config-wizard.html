<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NotifyMaster - Database Configuration Wizard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .wizard-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 100%;
            overflow: hidden;
        }

        .wizard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .wizard-header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .wizard-header p {
            opacity: 0.9;
            font-size: 16px;
        }

        .wizard-content {
            padding: 30px;
        }

        .step {
            display: none;
        }

        .step.active {
            display: block;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .provider-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .provider-card {
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s;
            text-align: center;
        }

        .provider-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .provider-card.selected {
            border-color: #667eea;
            background: #f8f9ff;
        }

        .provider-card.recommended::after {
            content: "Recommended";
            background: #10b981;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            position: absolute;
            top: 10px;
            right: 10px;
        }

        .provider-card {
            position: relative;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .btn:hover {
            transform: translateY(-1px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .btn-secondary {
            background: #6b7280;
        }

        .wizard-actions {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
        }

        .status-indicator {
            padding: 10px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 600;
        }

        .status-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fcd34d;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="wizard-container">
        <div class="wizard-header">
            <h1>🔧 Database Configuration</h1>
            <p>Configure your database connection for NotifyMaster</p>
        </div>

        <div class="wizard-content">
            <!-- Step 1: Provider Selection -->
            <div class="step active" id="step1">
                <h2>Step 1: Select Database Provider</h2>
                <p>Choose your preferred database provider:</p>
                
                <div class="provider-grid" id="providerGrid">
                    <!-- Providers will be loaded here -->
                </div>

                <div class="wizard-actions">
                    <button class="btn btn-secondary" onclick="skipWizard()">Skip (Use In-Memory)</button>
                    <button class="btn" onclick="nextStep()" id="nextBtn1" disabled>Next</button>
                </div>
            </div>

            <!-- Step 2: Connection Details -->
            <div class="step" id="step2">
                <h2>Step 2: Connection Details</h2>
                <div id="connectionForm">
                    <!-- Form fields will be dynamically generated -->
                </div>

                <div class="wizard-actions">
                    <button class="btn btn-secondary" onclick="prevStep()">Back</button>
                    <button class="btn" onclick="testConnection()" id="testBtn">Test Connection</button>
                </div>
            </div>

            <!-- Step 3: Test & Save -->
            <div class="step" id="step3">
                <h2>Step 3: Test & Save Configuration</h2>
                
                <div id="testResults">
                    <!-- Test results will appear here -->
                </div>

                <div class="form-group">
                    <label>Generated Connection String:</label>
                    <input type="text" id="finalConnectionString" readonly>
                </div>

                <div class="wizard-actions">
                    <button class="btn btn-secondary" onclick="prevStep()">Back</button>
                    <button class="btn" onclick="saveConfiguration()" id="saveBtn">Save Configuration</button>
                </div>
            </div>

            <!-- Step 4: Complete -->
            <div class="step" id="step4">
                <h2>✅ Configuration Complete</h2>
                <div class="status-success">
                    Database configuration has been saved successfully!
                </div>
                <p>Please restart the application to apply the new database configuration.</p>
                
                <div class="wizard-actions">
                    <button class="btn" onclick="window.location.reload()">Restart Application</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 1;
        let selectedProvider = null;
        let providers = [];
        let connectionData = {};

        // Initialize wizard
        document.addEventListener('DOMContentLoaded', async function() {
            await loadProviders();
            await checkCurrentStatus();
        });

        async function loadProviders() {
            try {
                const response = await fetch('/api/configuration/database/providers');
                providers = await response.json();
                
                const grid = document.getElementById('providerGrid');
                grid.innerHTML = providers.map(provider => `
                    <div class="provider-card ${provider.isRecommended ? 'recommended' : ''}" 
                         onclick="selectProvider('${provider.id}')" 
                         data-provider="${provider.id}">
                        <h3>${provider.name}</h3>
                        <p>${provider.description}</p>
                        ${provider.defaultPort ? `<small>Default Port: ${provider.defaultPort}</small>` : ''}
                    </div>
                `).join('');
            } catch (error) {
                console.error('Failed to load providers:', error);
            }
        }

        async function checkCurrentStatus() {
            try {
                const response = await fetch('/api/configuration/database/status');
                const status = await response.json();
                
                if (status.isConfigured && status.isValid) {
                    // Show current configuration status
                    document.querySelector('.wizard-header p').innerHTML = 
                        `Current database: ${status.provider} ✅ Connected`;
                }
            } catch (error) {
                console.error('Failed to check status:', error);
            }
        }

        function selectProvider(providerId) {
            selectedProvider = providerId;
            
            // Update UI
            document.querySelectorAll('.provider-card').forEach(card => {
                card.classList.remove('selected');
            });
            document.querySelector(`[data-provider="${providerId}"]`).classList.add('selected');
            
            document.getElementById('nextBtn1').disabled = false;
        }

        function nextStep() {
            if (currentStep === 1 && selectedProvider) {
                generateConnectionForm();
                showStep(2);
            } else if (currentStep === 2) {
                showStep(3);
            }
        }

        function prevStep() {
            if (currentStep > 1) {
                showStep(currentStep - 1);
            }
        }

        function showStep(step) {
            document.querySelectorAll('.step').forEach(s => s.classList.remove('active'));
            document.getElementById(`step${step}`).classList.add('active');
            currentStep = step;
        }

        function generateConnectionForm() {
            const form = document.getElementById('connectionForm');
            const provider = providers.find(p => p.id === selectedProvider);
            
            let formHtml = '';
            
            if (selectedProvider === 'sqlite') {
                formHtml = `
                    <div class="form-group">
                        <label for="filePath">Database File Path:</label>
                        <input type="text" id="filePath" value="./data/notifications.db" placeholder="./data/notifications.db">
                    </div>
                `;
            } else if (selectedProvider === 'sqlserver') {
                formHtml = `
                    <div class="form-group">
                        <label for="host">Server:</label>
                        <input type="text" id="host" value="localhost" placeholder="localhost">
                    </div>
                    <div class="form-group">
                        <label for="database">Database Name:</label>
                        <input type="text" id="database" placeholder="NotifyMaster" required>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="useWindowsAuth"> Use Windows Authentication
                        </label>
                    </div>
                    <div id="sqlCredentials">
                        <div class="form-group">
                            <label for="username">Username:</label>
                            <input type="text" id="username" placeholder="sa">
                        </div>
                        <div class="form-group">
                            <label for="password">Password:</label>
                            <input type="password" id="password">
                        </div>
                    </div>
                `;
            } else {
                formHtml = `
                    <div class="form-group">
                        <label for="host">Host:</label>
                        <input type="text" id="host" value="localhost" placeholder="localhost">
                    </div>
                    <div class="form-group">
                        <label for="port">Port:</label>
                        <input type="number" id="port" value="${provider?.defaultPort || ''}" placeholder="${provider?.defaultPort || ''}">
                    </div>
                    <div class="form-group">
                        <label for="database">Database Name:</label>
                        <input type="text" id="database" placeholder="NotifyMaster" required>
                    </div>
                    <div class="form-group">
                        <label for="username">Username:</label>
                        <input type="text" id="username" placeholder="postgres">
                    </div>
                    <div class="form-group">
                        <label for="password">Password:</label>
                        <input type="password" id="password">
                    </div>
                `;
                
                if (selectedProvider === 'postgresql') {
                    formHtml += `
                        <div class="form-group">
                            <label for="sslMode">SSL Mode:</label>
                            <select id="sslMode">
                                <option value="Prefer">Prefer</option>
                                <option value="Require">Require</option>
                                <option value="Disable">Disable</option>
                            </select>
                        </div>
                    `;
                }
            }
            
            form.innerHTML = formHtml;
            
            // Add event listener for Windows Auth checkbox
            const winAuthCheckbox = document.getElementById('useWindowsAuth');
            if (winAuthCheckbox) {
                winAuthCheckbox.addEventListener('change', function() {
                    const credentials = document.getElementById('sqlCredentials');
                    credentials.style.display = this.checked ? 'none' : 'block';
                });
            }
        }

        async function testConnection() {
            const testBtn = document.getElementById('testBtn');
            const originalText = testBtn.innerHTML;
            
            testBtn.innerHTML = '<span class="loading"></span> Testing...';
            testBtn.disabled = true;
            
            try {
                // Generate connection string
                const connectionString = await generateConnectionString();
                
                // Test connection
                const response = await fetch('/api/configuration/database/test', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ connectionString })
                });
                
                const result = await response.json();
                
                // Show results and move to next step
                document.getElementById('finalConnectionString').value = connectionString;
                
                const resultsDiv = document.getElementById('testResults');
                resultsDiv.innerHTML = `
                    <div class="status-indicator ${result.isSuccessful ? 'status-success' : 'status-error'}">
                        ${result.isSuccessful ? '✅' : '❌'} ${result.message}
                    </div>
                `;
                
                if (result.isSuccessful) {
                    connectionData.connectionString = connectionString;
                    showStep(3);
                }
                
            } catch (error) {
                const resultsDiv = document.getElementById('testResults');
                resultsDiv.innerHTML = `
                    <div class="status-indicator status-error">
                        ❌ Test failed: ${error.message}
                    </div>
                `;
            } finally {
                testBtn.innerHTML = originalText;
                testBtn.disabled = false;
            }
        }

        async function generateConnectionString() {
            const formData = {
                provider: selectedProvider,
                host: document.getElementById('host')?.value,
                port: document.getElementById('port')?.value ? parseInt(document.getElementById('port').value) : null,
                database: document.getElementById('database')?.value,
                username: document.getElementById('username')?.value,
                password: document.getElementById('password')?.value,
                sslMode: document.getElementById('sslMode')?.value,
                useWindowsAuth: document.getElementById('useWindowsAuth')?.checked || false,
                filePath: document.getElementById('filePath')?.value
            };
            
            const response = await fetch('/api/configuration/database/generate-connection-string', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(formData)
            });
            
            if (!response.ok) {
                throw new Error('Failed to generate connection string');
            }
            
            const result = await response.json();
            return result.connectionString;
        }

        async function saveConfiguration() {
            const saveBtn = document.getElementById('saveBtn');
            const originalText = saveBtn.innerHTML;
            
            saveBtn.innerHTML = '<span class="loading"></span> Saving...';
            saveBtn.disabled = true;
            
            try {
                const response = await fetch('/api/configuration/database/save', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        connectionString: connectionData.connectionString,
                        forceSkipValidation: false
                    })
                });
                
                const result = await response.json();
                
                if (result.isSuccessful) {
                    showStep(4);
                } else {
                    alert('Failed to save configuration: ' + result.message);
                }
                
            } catch (error) {
                alert('Failed to save configuration: ' + error.message);
            } finally {
                saveBtn.innerHTML = originalText;
                saveBtn.disabled = false;
            }
        }

        function skipWizard() {
            if (confirm('Are you sure you want to skip database configuration? The application will use an in-memory database.')) {
                window.location.href = '/';
            }
        }
    </script>
</body>
</html>
