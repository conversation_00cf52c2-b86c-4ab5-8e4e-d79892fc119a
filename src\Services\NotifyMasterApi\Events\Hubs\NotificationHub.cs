using Microsoft.AspNetCore.SignalR;
using NotifyMasterApi.Events.Models;

namespace NotifyMasterApi.Events.Hubs;

/// <summary>
/// SignalR hub for real-time notification events
/// </summary>
public class NotificationHub : Hub
{
    private readonly ILogger<NotificationHub> _logger;

    public NotificationHub(ILogger<NotificationHub> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Called when a client connects to the hub
    /// </summary>
    public override async Task OnConnectedAsync()
    {
        _logger.LogInformation("Client {ConnectionId} connected to NotificationHub", Context.ConnectionId);
        await base.OnConnectedAsync();
    }

    /// <summary>
    /// Called when a client disconnects from the hub
    /// </summary>
    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        _logger.LogInformation("Client {ConnectionId} disconnected from NotificationHub. Exception: {Exception}", 
            Context.ConnectionId, exception?.Message);
        await base.OnDisconnectedAsync(exception);
    }

    /// <summary>
    /// Subscribe to notification events for a specific user
    /// </summary>
    /// <param name="userId">User ID to subscribe to</param>
    public async Task SubscribeToUserNotifications(string userId)
    {
        var groupName = $"user_{userId}";
        await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
        _logger.LogInformation("Client {ConnectionId} subscribed to user notifications for {UserId}", 
            Context.ConnectionId, userId);
    }

    /// <summary>
    /// Unsubscribe from notification events for a specific user
    /// </summary>
    /// <param name="userId">User ID to unsubscribe from</param>
    public async Task UnsubscribeFromUserNotifications(string userId)
    {
        var groupName = $"user_{userId}";
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
        _logger.LogInformation("Client {ConnectionId} unsubscribed from user notifications for {UserId}", 
            Context.ConnectionId, userId);
    }

    /// <summary>
    /// Subscribe to notification events for a specific notification type
    /// </summary>
    /// <param name="notificationType">Type of notifications to subscribe to (Email, SMS, Push)</param>
    public async Task SubscribeToNotificationType(string notificationType)
    {
        var groupName = $"type_{notificationType.ToLower()}";
        await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
        _logger.LogInformation("Client {ConnectionId} subscribed to {NotificationType} notifications", 
            Context.ConnectionId, notificationType);
    }

    /// <summary>
    /// Unsubscribe from notification events for a specific notification type
    /// </summary>
    /// <param name="notificationType">Type of notifications to unsubscribe from</param>
    public async Task UnsubscribeFromNotificationType(string notificationType)
    {
        var groupName = $"type_{notificationType.ToLower()}";
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
        _logger.LogInformation("Client {ConnectionId} unsubscribed from {NotificationType} notifications", 
            Context.ConnectionId, notificationType);
    }

    /// <summary>
    /// Subscribe to notification events for a specific provider
    /// </summary>
    /// <param name="provider">Provider to subscribe to</param>
    public async Task SubscribeToProvider(string provider)
    {
        var groupName = $"provider_{provider.ToLower()}";
        await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
        _logger.LogInformation("Client {ConnectionId} subscribed to {Provider} provider notifications", 
            Context.ConnectionId, provider);
    }

    /// <summary>
    /// Unsubscribe from notification events for a specific provider
    /// </summary>
    /// <param name="provider">Provider to unsubscribe from</param>
    public async Task UnsubscribeFromProvider(string provider)
    {
        var groupName = $"provider_{provider.ToLower()}";
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
        _logger.LogInformation("Client {ConnectionId} unsubscribed from {Provider} provider notifications", 
            Context.ConnectionId, provider);
    }

    /// <summary>
    /// Subscribe to all notification events (admin/monitoring use)
    /// </summary>
    public async Task SubscribeToAllNotifications()
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, "all_notifications");
        _logger.LogInformation("Client {ConnectionId} subscribed to all notifications", Context.ConnectionId);
    }

    /// <summary>
    /// Unsubscribe from all notification events
    /// </summary>
    public async Task UnsubscribeFromAllNotifications()
    {
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, "all_notifications");
        _logger.LogInformation("Client {ConnectionId} unsubscribed from all notifications", Context.ConnectionId);
    }

    /// <summary>
    /// Subscribe to notifications for a specific correlation ID
    /// </summary>
    /// <param name="correlationId">Correlation ID to track</param>
    public async Task SubscribeToCorrelation(string correlationId)
    {
        var groupName = $"correlation_{correlationId}";
        await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
        _logger.LogInformation("Client {ConnectionId} subscribed to correlation {CorrelationId}", 
            Context.ConnectionId, correlationId);
    }

    /// <summary>
    /// Unsubscribe from notifications for a specific correlation ID
    /// </summary>
    /// <param name="correlationId">Correlation ID to stop tracking</param>
    public async Task UnsubscribeFromCorrelation(string correlationId)
    {
        var groupName = $"correlation_{correlationId}";
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
        _logger.LogInformation("Client {ConnectionId} unsubscribed from correlation {CorrelationId}", 
            Context.ConnectionId, correlationId);
    }
}

/// <summary>
/// SignalR hub for administrative events
/// </summary>
public class AdminHub : Hub
{
    private readonly ILogger<AdminHub> _logger;

    public AdminHub(ILogger<AdminHub> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Called when a client connects to the admin hub
    /// </summary>
    public override async Task OnConnectedAsync()
    {
        _logger.LogInformation("Admin client {ConnectionId} connected to AdminHub", Context.ConnectionId);
        await base.OnConnectedAsync();
    }

    /// <summary>
    /// Called when a client disconnects from the admin hub
    /// </summary>
    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        _logger.LogInformation("Admin client {ConnectionId} disconnected from AdminHub. Exception: {Exception}", 
            Context.ConnectionId, exception?.Message);
        await base.OnDisconnectedAsync(exception);
    }

    /// <summary>
    /// Subscribe to system health events
    /// </summary>
    public async Task SubscribeToSystemHealth()
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, "system_health");
        _logger.LogInformation("Admin client {ConnectionId} subscribed to system health events", Context.ConnectionId);
    }

    /// <summary>
    /// Subscribe to plugin management events
    /// </summary>
    public async Task SubscribeToPluginEvents()
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, "plugin_events");
        _logger.LogInformation("Admin client {ConnectionId} subscribed to plugin events", Context.ConnectionId);
    }

    /// <summary>
    /// Subscribe to metrics updates
    /// </summary>
    public async Task SubscribeToMetrics()
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, "metrics_updates");
        _logger.LogInformation("Admin client {ConnectionId} subscribed to metrics updates", Context.ConnectionId);
    }

    /// <summary>
    /// Subscribe to system error events
    /// </summary>
    public async Task SubscribeToSystemErrors()
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, "system_errors");
        _logger.LogInformation("Admin client {ConnectionId} subscribed to system errors", Context.ConnectionId);
    }

    /// <summary>
    /// Subscribe to all admin events
    /// </summary>
    public async Task SubscribeToAllAdminEvents()
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, "all_admin_events");
        _logger.LogInformation("Admin client {ConnectionId} subscribed to all admin events", Context.ConnectionId);
    }
}
