using System.ComponentModel.DataAnnotations;
using NotificationContract.Enums;

namespace NotifyMasterApi.Infrastructure.Entities;

/// <summary>
/// Represents a notification log entry in the database.
/// Tracks the lifecycle and status of notifications sent through the system.
/// </summary>
public class NotificationLog
{
    /// <summary>
    /// Gets or sets the unique identifier for the notification log entry.
    /// </summary>
    [Key]
    public Guid Id { get; set; }
    
    /// <summary>
    /// Gets or sets the unique message identifier from the notification provider.
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string MessageId { get; set; } = string.Empty;
    
    /// <summary>
    /// Gets or sets the type of notification (Email, SMS, Push).
    /// </summary>
    [Required]
    public NotificationType Type { get; set; }
    
    /// <summary>
    /// Gets or sets the recipient of the notification.
    /// For email: email address, for SMS: phone number, for push: device token.
    /// </summary>
    [Required]
    [MaxLength(500)]
    public string Recipient { get; set; } = string.Empty;
    
    /// <summary>
    /// Gets or sets the subject of the notification (primarily for emails).
    /// </summary>
    [MaxLength(200)]
    public string? Subject { get; set; }
    
    /// <summary>
    /// Gets or sets the content/body of the notification.
    /// </summary>
    [Required]
    public string Content { get; set; } = string.Empty;
    
    /// <summary>
    /// Gets or sets the current status of the notification.
    /// </summary>
    [Required]
    public NotificationStatus Status { get; set; }
    
    /// <summary>
    /// Gets or sets the provider used to send the notification.
    /// </summary>
    [MaxLength(100)]
    public string? Provider { get; set; }
    
    /// <summary>
    /// Gets or sets the error message if the notification failed.
    /// </summary>
    [MaxLength(1000)]
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// Gets or sets the response data from the notification provider.
    /// Stored as JSON for flexible data structure.
    /// </summary>
    public Dictionary<string, object>? ResponseData { get; set; }
    
    /// <summary>
    /// Gets or sets when the notification was created.
    /// </summary>
    [Required]
    public DateTime CreatedAt { get; set; }
    
    /// <summary>
    /// Gets or sets when the notification was sent.
    /// </summary>
    public DateTime? SentAt { get; set; }
    
    /// <summary>
    /// Gets or sets when the notification was delivered.
    /// </summary>
    public DateTime? DeliveredAt { get; set; }
    
    /// <summary>
    /// Gets or sets when the notification failed.
    /// </summary>
    public DateTime? FailedAt { get; set; }
    
    /// <summary>
    /// Gets or sets the number of retry attempts made.
    /// </summary>
    public int RetryCount { get; set; } = 0;
    
    /// <summary>
    /// Gets or sets when the last retry attempt was made.
    /// </summary>
    public DateTime? LastRetryAt { get; set; }
    
    /// <summary>
    /// Gets or sets the correlation ID for tracking related operations.
    /// </summary>
    [MaxLength(50)]
    public string? CorrelationId { get; set; }
    
    /// <summary>
    /// Gets or sets the user ID associated with the notification.
    /// </summary>
    [MaxLength(100)]
    public string? UserId { get; set; }
    
    /// <summary>
    /// Gets or sets additional metadata associated with the notification.
    /// Stored as JSON for flexible data structure.
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }
}
