using System.ComponentModel.DataAnnotations;
using NotificationContract.Enums;

namespace NotifyMasterApi.Infrastructure.Entities;

/// <summary>
/// Represents an error that occurred during notification processing.
/// Used for error tracking, debugging, and system monitoring.
/// </summary>
public class NotificationError
{
    /// <summary>
    /// Gets or sets the unique identifier for the error entry.
    /// </summary>
    [Key]
    public Guid Id { get; set; }
    
    /// <summary>
    /// Gets or sets the type of notification that caused the error.
    /// </summary>
    [Required]
    public NotificationType Type { get; set; }
    
    /// <summary>
    /// Gets or sets the provider that was being used when the error occurred.
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string Provider { get; set; } = string.Empty;
    
    /// <summary>
    /// Gets or sets the error code from the provider or system.
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string ErrorCode { get; set; } = string.Empty;
    
    /// <summary>
    /// Gets or sets the detailed error message.
    /// </summary>
    [Required]
    [MaxLength(1000)]
    public string ErrorMessage { get; set; } = string.Empty;
    
    /// <summary>
    /// Gets or sets the recipient that was being notified when the error occurred.
    /// </summary>
    [MaxLength(500)]
    public string? Recipient { get; set; }
    
    /// <summary>
    /// Gets or sets the message ID associated with the failed notification.
    /// </summary>
    [MaxLength(100)]
    public string? MessageId { get; set; }
    
    /// <summary>
    /// Gets or sets the stack trace of the error for debugging purposes.
    /// </summary>
    public string? StackTrace { get; set; }
    
    /// <summary>
    /// Gets or sets the request data that was being processed when the error occurred.
    /// Stored as JSON for flexible data structure.
    /// </summary>
    public string? RequestData { get; set; }
    
    /// <summary>
    /// Gets or sets when the error occurred.
    /// </summary>
    [Required]
    public DateTime OccurredAt { get; set; }
    
    /// <summary>
    /// Gets or sets the severity level of the error.
    /// 1=Low, 2=Medium, 3=High, 4=Critical
    /// </summary>
    public int Severity { get; set; } = 2;
    
    /// <summary>
    /// Gets or sets whether this error has been resolved.
    /// </summary>
    public bool IsResolved { get; set; } = false;
    
    /// <summary>
    /// Gets or sets when this error was resolved.
    /// </summary>
    public DateTime? ResolvedAt { get; set; }
    
    /// <summary>
    /// Gets or sets who resolved this error.
    /// </summary>
    [MaxLength(100)]
    public string? ResolvedBy { get; set; }
    
    /// <summary>
    /// Gets or sets notes about the error resolution.
    /// </summary>
    [MaxLength(1000)]
    public string? ResolutionNotes { get; set; }
}
