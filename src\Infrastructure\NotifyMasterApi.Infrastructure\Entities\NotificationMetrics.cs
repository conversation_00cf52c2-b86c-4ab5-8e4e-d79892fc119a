using System.ComponentModel.DataAnnotations;
using NotificationContract.Enums;

namespace NotifyMasterApi.Infrastructure.Entities;

/// <summary>
/// Represents aggregated metrics for notification performance and statistics.
/// Used for monitoring and reporting notification system performance.
/// </summary>
public class NotificationMetrics
{
    /// <summary>
    /// Gets or sets the unique identifier for the metrics entry.
    /// </summary>
    [Key]
    public Guid Id { get; set; }
    
    /// <summary>
    /// Gets or sets the type of notification these metrics apply to.
    /// </summary>
    [Required]
    public NotificationType Type { get; set; }
    
    /// <summary>
    /// Gets or sets the provider these metrics apply to.
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string Provider { get; set; } = string.Empty;
    
    /// <summary>
    /// Gets or sets the date these metrics are for.
    /// </summary>
    [Required]
    public DateTime Date { get; set; }
    
    /// <summary>
    /// Gets or sets the total number of notifications sent.
    /// </summary>
    public int TotalSent { get; set; }
    
    /// <summary>
    /// Gets or sets the total number of notifications delivered successfully.
    /// </summary>
    public int TotalDelivered { get; set; }
    
    /// <summary>
    /// Gets or sets the total number of notifications that failed.
    /// </summary>
    public int TotalFailed { get; set; }
    
    /// <summary>
    /// Gets or sets the total number of retry attempts made.
    /// </summary>
    public int TotalRetries { get; set; }
    
    /// <summary>
    /// Gets or sets the average response time in milliseconds.
    /// </summary>
    public double AverageResponseTime { get; set; }
    
    /// <summary>
    /// Gets or sets the success rate as a percentage (0-100).
    /// </summary>
    public double SuccessRate { get; set; }
    
    /// <summary>
    /// Gets or sets the failure rate as a percentage (0-100).
    /// </summary>
    public double FailureRate { get; set; }
    
    /// <summary>
    /// Gets or sets when this metrics entry was created.
    /// </summary>
    [Required]
    public DateTime CreatedAt { get; set; }
    
    /// <summary>
    /// Gets or sets when this metrics entry was last updated.
    /// </summary>
    public DateTime UpdatedAt { get; set; }
}
