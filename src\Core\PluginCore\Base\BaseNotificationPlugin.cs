using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using PluginContract.Interfaces;
using PluginContract.Models;

namespace PluginCore.Base;

///<summary>
///  Base class for all notification plugins
/// </summary>
/// <remarks>
/// This class provides a base implementation for all notification plugins, including:
/// - Plugin information
/// - Configuration services
/// - Configuration validation
/// - Initialization
/// - Sending notifications
/// - Health checks
/// - Configuration section
/// - Configuration retrieval
/// - Disposal
/// </remarks>
public abstract class BaseNotificationPlugin : INotificationPlugin, IDisposable
{
    protected ILogger? Logger { get; private set; }
    protected IConfiguration? Configuration { get; private set; }

    public abstract PluginInfo PluginInfo { get; }
/// <summary>
///  Configures the services for the plugin
/// </summary>
/// <param name="services"> Service collection to configure </param>
/// <param name="configuration"> Configuration to use </param>
    public virtual void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        // Default implementation - plugins can override
    }
/// <summary>
///  Validates the configuration for the plugin
/// </summary>
/// <param name="configuration"> Configuration to validate </param>
/// <returns> True if the configuration is valid, false otherwise </returns>
    public virtual Task<bool> ValidateConfigurationAsync(IConfiguration configuration)
    {
        // Default implementation - plugins should override for specific validation
        return Task.FromResult(true);
    }
/// <summary>
///  Initializes the plugin
/// </summary>
/// <param name="configuration"> Configuration to use </param>
/// <returns> True if the initialization was successful, false otherwise </returns>
    public virtual Task InitializeAsync(IConfiguration configuration)
    {
        Configuration = configuration;
        
        // Create logger if available
        var serviceProvider = new ServiceCollection()
            .AddLogging()
            .BuildServiceProvider();
        Logger = serviceProvider.GetService<ILoggerFactory>()?.CreateLogger(GetType());
        return Task.CompletedTask;
    }
/// <summary>
///  Sends a notification
/// </summary>
/// <param name="request"> Notification request to send </param>
/// <param name="cancellationToken"> Cancellation token </param>
/// <returns> Notification response </returns>
    public abstract Task<NotificationResponse> SendAsync(NotificationRequest request, CancellationToken cancellationToken = default);

/// <summary>
///  Performs a health check for the plugin
/// </summary>
/// <returns> True if the plugin is healthy, false otherwise </returns>
    public virtual Task<bool> HealthCheckAsync()
    {
        return Task.FromResult(true);
    }

/// <summary>
///  Gets the configuration section for the plugin
/// </summary>
/// <returns> Configuration section </returns>
    protected virtual string GetConfigurationSection()
    {
        return $"Plugins:{PluginInfo.Name}";
    }

/// <summary>
///  Gets the configuration for the plugin
/// </summary>
/// <typeparam name="T"> Type of the configuration </typeparam>
/// <returns> Configuration </returns>
    protected virtual T? GetConfiguration<T>() where T : class
    {
        if (Configuration == null) return null;

        var section = Configuration.GetSection(GetConfigurationSection());
        return section.Get<T>();
    }
/// <summary>
///  Disposes the plugin
/// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
/// <summary>
///  Disposes the plugin
/// </summary>
/// <param name="disposing"> True if the plugin is being disposed </param>
    protected virtual void Dispose(bool disposing)
    {
        // Base implementation - plugins can override for cleanup
    }
}
