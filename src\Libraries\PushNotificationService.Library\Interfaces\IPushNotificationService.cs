using NotificationContract.Models;
using Microsoft.AspNetCore.Http;

namespace PushNotificationService.Library.Interfaces;

public interface IPushNotificationService
{
    // Core push notification functionality
    Task<PushResponse> SendAsync(PushMessageRequest request);
    Task<BulkPushResponse> SendBulkAsync(BulkPushRequest request);
    Task<MessageStatusResponse> GetMessageStatusAsync(string messageId);
    Task<MessageHistoryResponse> GetMessageHistoryAsync(string deviceToken);
    Task<PushResponse> ResendMessageAsync(string messageId);

    // Admin functionality
    Task<object> GetServiceStatusAsync();
    Task<object> GetPlatformsAsync();
    Task<ServiceResult> ConfigurePlatformAsync(string platform, object configuration);
    Task<object> TestPlatformAsync(string platform, string? testToken = null);
    Task<ServiceResult> UpdatePlatformStatusAsync(string platform, bool enabled);
    Task<object> GetConfigurationAsync();
    Task<ServiceResult> UpdateConfigurationAsync(object configuration);
    Task ClearCacheAsync();
    Task<object> GetQueueStatusAsync();
    Task<ServiceResult> PurgeQueueAsync();
    Task<object> CleanupDeviceTokensAsync(string? platform = null, bool dryRun = true);
    Task<object> GetIosCertificateStatusAsync();
    Task<ServiceResult> UpdateIosCertificateAsync(IFormFile certificate);

    // Metrics functionality
    Task<object> GetSummaryMetricsAsync();
    Task<object> GetDetailedMetricsAsync(DateTime? startDate = null, DateTime? endDate = null, string? platform = null);
    Task<object> GetErrorMetricsAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<object> GetMonthlyMetricsAsync(int months = 12);
    Task<object> GetPerformanceMetricsAsync();
    Task<object> GetDeliveryRateMetricsAsync(string? platform = null);
    Task<object> GetEngagementMetricsAsync(DateTime? startDate = null, DateTime? endDate = null, string? platform = null);
    Task<object> GetDeviceTokenMetricsAsync(string? platform = null);
    Task<object> GetMonthlyStatisticsAsync();
}


