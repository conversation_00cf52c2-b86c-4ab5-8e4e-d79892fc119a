{"name": "MessageBird SMS Provider", "description": "MessageBird SMS provider plugin with global reach and omnichannel capabilities", "version": "1.0.0", "author": "NotifyMaster Team", "type": "SMS", "provider": "MessageBird", "assemblyName": "Plugin.Sms.MessageBird.dll", "className": "Plugin.Sms.MessageBird.MessageBirdPlugin", "supportedFeatures": ["SMS", "Unicode", "DeliveryReceipts", "LongMessages", "BulkSending", "GlobalCoverage", "TwoWayMessaging", "NumberLookup", "VoiceMessages", "FlashSMS"], "configuration": {"accessKey": {"type": "string", "required": true, "description": "MessageBird Access Key", "sensitive": true}, "defaultOriginator": {"type": "string", "required": false, "description": "Default sender ID or phone number", "sensitive": false, "default": "NotifyMaster"}, "enableDeliveryReports": {"type": "boolean", "required": false, "description": "Enable delivery report tracking", "default": true}, "messageType": {"type": "string", "required": false, "description": "Message type (sms, binary, flash)", "default": "sms", "allowedValues": ["sms", "binary", "flash"]}, "validity": {"type": "integer", "required": false, "description": "Message validity period in seconds", "default": 259200}, "datacoding": {"type": "string", "required": false, "description": "Data coding scheme (plain, unicode, auto)", "default": "auto", "allowedValues": ["plain", "unicode", "auto"]}, "gateway": {"type": "integer", "required": false, "description": "Gateway ID for routing", "default": null}, "groupIds": {"type": "array", "required": false, "description": "Group IDs for contact management", "default": []}, "scheduledDatetime": {"type": "string", "required": false, "description": "Schedule message for future delivery (ISO 8601 format)", "default": null}}, "dependencies": [{"name": "MessageBird", "version": "2.0.0", "type": "NuGet"}], "capabilities": {"maxMessageLength": 1600, "supportsConcatenation": true, "supportsUnicode": true, "supportsDeliveryReceipts": true, "supportsBulkSending": true, "supportsScheduling": true, "supportsFlashSMS": true, "rateLimit": {"requestsPerSecond": 20, "burstLimit": 100}, "globalCoverage": true, "supportedCountries": ["*"], "pricing": {"model": "per-message", "currency": "EUR", "basePrice": 0.05}}, "metadata": {"website": "https://www.messagebird.com/", "documentation": "https://developers.messagebird.com/", "support": "https://support.messagebird.com/", "tags": ["sms", "messaging", "global", "omnichannel", "messagebird"], "category": "Communication", "license": "MIT"}}