using NotifyMasterApi.Interfaces;

namespace NotifyMasterApi.Services;

/// <summary>
/// Background service that automatically loads plugins at startup
/// </summary>
/// <remarks>
/// This service automatically loads all plugins from the configured plugins directory at startup.
/// It uses the IPluginManager to load the plugins and check their health status.
/// </remarks>
public class PluginAutoLoaderService : BackgroundService
{
    private readonly ILogger<PluginAutoLoaderService> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly IConfiguration _configuration;

    public PluginAutoLoaderService(
        ILogger<PluginAutoLoaderService> logger,
        IServiceProvider serviceProvider,
        IConfiguration configuration)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
        _configuration = configuration;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        try
        {
            // Wait a bit for the application to fully start
            await Task.Delay(2000, stoppingToken);

            _logger.LogInformation("Starting plugin auto-loader service...");

            using var scope = _serviceProvider.CreateScope();
            var pluginManager = scope.ServiceProvider.GetRequiredService<IPluginManager>();

            if (pluginManager is RuntimePluginManager runtimeManager)
            {
                // Load all plugins from the plugins directory
                var loadedCount = await runtimeManager.LoadAllPluginsAsync();
                _logger.LogInformation("Auto-loaded {LoadedCount} plugins", loadedCount);

                // Get health status of all loaded plugins
                var healthStatus = await runtimeManager.GetPluginsHealthStatusAsync();
                foreach (var (pluginName, isHealthy) in healthStatus)
                {
                    _logger.LogInformation("Plugin {PluginName} health status: {IsHealthy}", pluginName, isHealthy);
                }
            }
            else
            {
                _logger.LogWarning("Plugin manager is not a RuntimePluginManager, skipping auto-load");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in plugin auto-loader service");
        }
    }
}
