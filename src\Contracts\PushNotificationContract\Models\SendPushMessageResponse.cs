using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace PushNotificationContract.Models;

/// <summary>
/// Represents the response from a push notification send operation.
/// </summary>
/// <remarks>
/// This response provides information about the push notification sending attempt,
/// including success status, message identifiers, and any error details.
/// </remarks>
public sealed record SendPushMessageResponse
{
    /// <summary>
    /// Gets a value indicating whether the push notification was successfully sent.
    /// </summary>
    [JsonPropertyName("success")]
    public required bool Success { get; init; }

    /// <summary>
    /// Gets the unique identifier for this push notification message.
    /// </summary>
    /// <remarks>
    /// This ID can be used to track the notification status or reference
    /// the message in support requests.
    /// </remarks>
    /// <example>push_1234567890abcdef</example>
    [JsonPropertyName("messageId")]
    [StringLength(100, ErrorMessage = "Message ID cannot exceed 100 characters")]
    public string? MessageId { get; init; }

    /// <summary>
    /// Gets the provider-specific message identifier.
    /// </summary>
    /// <remarks>
    /// This is the ID returned by the push notification service provider (e.g., Firebase).
    /// It may differ from the internal MessageId and can be used for provider-specific tracking.
    /// </remarks>
    /// <example>projects/myproject/messages/0:1234567890123456%31bd1c9631bd1c96</example>
    [JsonPropertyName("providerMessageId")]
    [StringLength(200, ErrorMessage = "Provider message ID cannot exceed 200 characters")]
    public string? ProviderMessageId { get; init; }

    /// <summary>
    /// Gets the current status of the push notification.
    /// </summary>
    /// <remarks>
    /// Common statuses include: Sent, Delivered, Failed, Invalid Token
    /// </remarks>
    /// <example>Sent</example>
    [JsonPropertyName("status")]
    [StringLength(50, ErrorMessage = "Status cannot exceed 50 characters")]
    public string? Status { get; init; }

    /// <summary>
    /// Gets the error message if the push notification sending failed.
    /// </summary>
    /// <example>Invalid device token</example>
    [JsonPropertyName("errorMessage")]
    [StringLength(1000, ErrorMessage = "Error message cannot exceed 1000 characters")]
    public string? ErrorMessage { get; init; }

    /// <summary>
    /// Gets the timestamp when the push notification was processed.
    /// </summary>
    [JsonPropertyName("timestamp")]
    public DateTime Timestamp { get; init; } = DateTime.UtcNow;

    /// <summary>
    /// Gets the name of the provider used to send the push notification.
    /// </summary>
    /// <example>Firebase</example>
    [JsonPropertyName("provider")]
    [StringLength(50, ErrorMessage = "Provider name cannot exceed 50 characters")]
    public string? Provider { get; init; }

    /// <summary>
    /// Gets the device token that was targeted.
    /// </summary>
    /// <remarks>
    /// This is useful for tracking which device received the notification,
    /// especially in batch operations.
    /// </remarks>
    [JsonPropertyName("deviceToken")]
    [StringLength(500, ErrorMessage = "Device token cannot exceed 500 characters")]
    public string? DeviceToken { get; init; }

    /// <summary>
    /// Gets additional metadata about the push notification sending operation.
    /// </summary>
    [JsonPropertyName("metadata")]
    public Dictionary<string, object>? Metadata { get; init; }

    /// <summary>
    /// Creates a successful push notification response.
    /// </summary>
    /// <param name="messageId">The message identifier</param>
    /// <param name="providerMessageId">The provider-specific message identifier</param>
    /// <param name="deviceToken">The device token that was targeted</param>
    /// <param name="provider">The provider used to send the notification</param>
    /// <param name="status">The current status of the notification</param>
    /// <returns>A successful SendPushMessageResponse</returns>
    public static SendPushMessageResponse CreateSuccess(
        string messageId,
        string? providerMessageId = null,
        string? deviceToken = null,
        string? provider = null,
        string status = "Sent")
    {
        return new SendPushMessageResponse
        {
            Success = true,
            MessageId = messageId,
            ProviderMessageId = providerMessageId,
            DeviceToken = deviceToken,
            Status = status,
            Provider = provider,
            Timestamp = DateTime.UtcNow
        };
    }

    /// <summary>
    /// Creates a failed push notification response.
    /// </summary>
    /// <param name="errorMessage">The error message describing the failure</param>
    /// <param name="messageId">The message identifier, if available</param>
    /// <param name="deviceToken">The device token that was attempted</param>
    /// <param name="provider">The provider that was attempted</param>
    /// <returns>A failed SendPushMessageResponse</returns>
    public static SendPushMessageResponse CreateFailure(
        string errorMessage,
        string? messageId = null,
        string? deviceToken = null,
        string? provider = null)
    {
        return new SendPushMessageResponse
        {
            Success = false,
            MessageId = messageId,
            DeviceToken = deviceToken,
            ErrorMessage = errorMessage,
            Status = "Failed",
            Provider = provider,
            Timestamp = DateTime.UtcNow
        };
    }
}
