using NotificationContract.Models;

namespace NotifyMasterApi.Gateways;

public interface IEmailGateway
{
    Task<EmailResponse> SendAsync(EmailMessageRequest request);
    Task<BulkEmailResponse> SendBulkAsync(BulkEmailRequest request);
    Task<EmailResponse> ResendMessageAsync(string messageId);
    Task<object> GetProvidersAsync();
    Task<ServiceResult> ConfigureProviderAsync(string provider, object configuration);
    Task<object> TestProviderAsync(string provider, string? testEmail = null);
    Task<ServiceResult> UpdateProviderStatusAsync(string provider, bool enabled);
    Task<object> GetDeliveryMetricsAsync(string? provider = null);
    Task<object> GetProviderMetricsAsync(string provider);

    // Additional methods expected by controllers
    Task<EmailResponse> SendEmailAsync(EmailMessageRequest request);
    Task<object> GetMessageStatusAsync(string messageId);
    Task<object> GetMessageHistoryAsync(string? userId = null, int page = 1, int pageSize = 50);
    Task<object> GetAvailableProviders();
    string? GetCurrentProvider();
    Task<ServiceResult> SwitchProvider(string provider);
    Task<object> SendTestMessageAsync(string provider, string? testEmail = null);
    Task<ServiceResult> ReloadProviders();
    Task<ServiceResult> UpdateProviderConfiguration(string provider, object configuration);
    Task<object> GetSummaryMetricsAsync();
    Task<object> GetDetailedMetricsAsync(DateTime? from = null, DateTime? to = null);
    Task<object> GetErrorMetricsAsync();
    Task<object> GetMonthlyStatisticsAsync(int year, int month);
}
