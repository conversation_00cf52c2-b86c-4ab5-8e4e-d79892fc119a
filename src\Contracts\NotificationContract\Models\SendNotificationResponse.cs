using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace NotificationContract.Models;

/// <summary>
/// Represents the response from a unified notification send operation.
/// </summary>
/// <remarks>
/// This response provides information about the notification sending attempt across
/// different channels (Email, SMS, Push), including success status, message identifiers,
/// and any error details. It supports multi-channel notifications and partial success scenarios.
/// </remarks>
public sealed record SendNotificationResponse
{
    /// <summary>
    /// Gets a value indicating whether the notification was successfully sent or queued.
    /// </summary>
    /// <remarks>
    /// For multi-channel notifications, this indicates overall success. Check individual
    /// channel results for detailed status information.
    /// </remarks>
    [JsonPropertyName("success")]
    public required bool Success { get; init; }

    /// <summary>
    /// Gets the unique identifier for this notification operation.
    /// </summary>
    /// <remarks>
    /// This ID can be used to track the notification status across all channels,
    /// retrieve delivery information, or reference the operation in support requests.
    /// </remarks>
    /// <example>notif_1234567890abcdef</example>
    [JsonPropertyName("notificationId")]
    [StringLength(100, ErrorMessage = "Notification ID cannot exceed 100 characters")]
    public string? NotificationId { get; init; }

    /// <summary>
    /// Gets the type of notification that was sent.
    /// </summary>
    /// <example>Email</example>
    [JsonPropertyName("notificationType")]
    [StringLength(50, ErrorMessage = "Notification type cannot exceed 50 characters")]
    public string? NotificationType { get; init; }

    /// <summary>
    /// Gets the current status of the notification.
    /// </summary>
    /// <remarks>
    /// Common statuses include: Queued, Sent, Delivered, Failed, Partial Success
    /// </remarks>
    /// <example>Queued</example>
    [JsonPropertyName("status")]
    [StringLength(50, ErrorMessage = "Status cannot exceed 50 characters")]
    public string? Status { get; init; }

    /// <summary>
    /// Gets the error message if the notification sending failed.
    /// </summary>
    /// <example>Invalid recipient information</example>
    [JsonPropertyName("errorMessage")]
    [StringLength(1000, ErrorMessage = "Error message cannot exceed 1000 characters")]
    public string? ErrorMessage { get; init; }

    /// <summary>
    /// Gets the timestamp when the notification was processed.
    /// </summary>
    [JsonPropertyName("timestamp")]
    public DateTime Timestamp { get; init; } = DateTime.UtcNow;

    /// <summary>
    /// Gets the name of the provider used to send the notification.
    /// </summary>
    /// <example>SendGrid</example>
    [JsonPropertyName("provider")]
    [StringLength(50, ErrorMessage = "Provider name cannot exceed 50 characters")]
    public string? Provider { get; init; }

    /// <summary>
    /// Gets the recipient information for tracking purposes.
    /// </summary>
    /// <example><EMAIL></example>
    [JsonPropertyName("recipient")]
    [StringLength(500, ErrorMessage = "Recipient information cannot exceed 500 characters")]
    public string? Recipient { get; init; }

    /// <summary>
    /// Gets detailed results for each channel when sending multi-channel notifications.
    /// </summary>
    /// <remarks>
    /// This dictionary contains channel-specific results with the channel name as key
    /// and the result details as value. Useful for tracking partial success scenarios.
    /// </remarks>
    [JsonPropertyName("channelResults")]
    public Dictionary<string, object>? ChannelResults { get; init; }

    /// <summary>
    /// Gets additional metadata about the notification sending operation.
    /// </summary>
    [JsonPropertyName("metadata")]
    public Dictionary<string, object>? Metadata { get; init; }

    /// <summary>
    /// Creates a successful notification response.
    /// </summary>
    /// <param name="notificationId">The notification identifier</param>
    /// <param name="notificationType">The type of notification sent</param>
    /// <param name="recipient">The recipient information</param>
    /// <param name="provider">The provider used to send the notification</param>
    /// <param name="status">The current status of the notification</param>
    /// <param name="channelResults">Results for individual channels</param>
    /// <returns>A successful SendNotificationResponse</returns>
    public static SendNotificationResponse CreateSuccess(
        string notificationId,
        string? notificationType = null,
        string? recipient = null,
        string? provider = null,
        string status = "Queued",
        Dictionary<string, object>? channelResults = null)
    {
        return new SendNotificationResponse
        {
            Success = true,
            NotificationId = notificationId,
            NotificationType = notificationType,
            Recipient = recipient,
            Status = status,
            Provider = provider,
            ChannelResults = channelResults,
            Timestamp = DateTime.UtcNow
        };
    }

    /// <summary>
    /// Creates a failed notification response.
    /// </summary>
    /// <param name="errorMessage">The error message describing the failure</param>
    /// <param name="notificationId">The notification identifier, if available</param>
    /// <param name="notificationType">The type of notification that was attempted</param>
    /// <param name="recipient">The recipient that was attempted</param>
    /// <param name="provider">The provider that was attempted</param>
    /// <param name="channelResults">Results for individual channels if applicable</param>
    /// <returns>A failed SendNotificationResponse</returns>
    public static SendNotificationResponse CreateFailure(
        string errorMessage,
        string? notificationId = null,
        string? notificationType = null,
        string? recipient = null,
        string? provider = null,
        Dictionary<string, object>? channelResults = null)
    {
        return new SendNotificationResponse
        {
            Success = false,
            NotificationId = notificationId,
            NotificationType = notificationType,
            Recipient = recipient,
            ErrorMessage = errorMessage,
            Status = "Failed",
            Provider = provider,
            ChannelResults = channelResults,
            Timestamp = DateTime.UtcNow
        };
    }

    /// <summary>
    /// Creates a partial success notification response for multi-channel scenarios.
    /// </summary>
    /// <param name="notificationId">The notification identifier</param>
    /// <param name="notificationType">The type of notification sent</param>
    /// <param name="recipient">The recipient information</param>
    /// <param name="channelResults">Results for individual channels</param>
    /// <param name="errorMessage">Overall error message if applicable</param>
    /// <returns>A partial success SendNotificationResponse</returns>
    public static SendNotificationResponse CreatePartialSuccess(
        string notificationId,
        string? notificationType = null,
        string? recipient = null,
        Dictionary<string, object>? channelResults = null,
        string? errorMessage = null)
    {
        return new SendNotificationResponse
        {
            Success = true,
            NotificationId = notificationId,
            NotificationType = notificationType,
            Recipient = recipient,
            Status = "Partial Success",
            ErrorMessage = errorMessage,
            ChannelResults = channelResults,
            Timestamp = DateTime.UtcNow
        };
    }
}
