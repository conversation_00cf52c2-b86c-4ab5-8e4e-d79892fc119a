namespace NotificationContract.Enums;

/// <summary>
///  Enum representing the type of a notification
/// </summary>
/// <remarks>
/// This enum represents the different types of notifications, including:
/// - Sms: A text message sent via SMS
/// - Email: An email message
/// - PushMessage: A push notification sent to a mobile device
/// </remarks>
public enum NotificationType
{
    Sms,
    Email,
    PushMessage,
}