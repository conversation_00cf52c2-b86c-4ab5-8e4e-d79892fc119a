using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace NotifyMasterApi.Persistence.Services;

/// <summary>
/// Background service that activates database fallback mechanisms.
/// Ensures the in-memory database service is properly initialized when the main database is unavailable.
/// </summary>
public class DatabaseFallbackActivationService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<DatabaseFallbackActivationService> _logger;

    /// <summary>
    /// Initializes a new instance of the DatabaseFallbackActivationService.
    /// </summary>
    /// <param name="serviceProvider">The service provider.</param>
    /// <param name="logger">The logger.</param>
    public DatabaseFallbackActivationService(
        IServiceProvider serviceProvider,
        ILogger<DatabaseFallbackActivationService> logger)
    {
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Executes the background service.
    /// </summary>
    /// <param name="stoppingToken">The cancellation token.</param>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("🔄 Database fallback activation service starting...");

        try
        {
            // Create a scope to access scoped services
            using var scope = _serviceProvider.CreateScope();
            
            // Get the in-memory database service to ensure it's initialized
            var inMemoryService = scope.ServiceProvider.GetRequiredService<InMemoryDatabaseService>();
            
            _logger.LogInformation("✅ In-memory database service activated successfully");
            _logger.LogInformation("📊 Current data count: {DataCount}", inMemoryService.DataCount);

            // Store initial system information
            inMemoryService.StoreData("system_startup", new
            {
                StartupTime = DateTime.UtcNow,
                Mode = "Fallback",
                ServiceName = "NotifyMasterApi",
                Version = "1.0.0"
            });

            _logger.LogInformation("🎯 Database fallback mode is now active and ready");

            // Keep the service running but don't do anything else
            await Task.Delay(Timeout.Infinite, stoppingToken);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("🛑 Database fallback activation service is stopping...");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Error in database fallback activation service");
            throw;
        }
    }

    /// <summary>
    /// Stops the background service.
    /// </summary>
    /// <param name="cancellationToken">The cancellation token.</param>
    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("🔄 Database fallback activation service stopping...");
        await base.StopAsync(cancellationToken);
        _logger.LogInformation("✅ Database fallback activation service stopped");
    }
}
