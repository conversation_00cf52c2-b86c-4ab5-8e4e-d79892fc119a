using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using NotifyMasterApi.Infrastructure.Interfaces;
using NotifyMasterApi.Persistence.Data;
using NotifyMasterApi.Persistence.Repositories;
using NotifyMasterApi.Persistence.Services;

namespace NotifyMasterApi.Persistence.Extensions;

/// <summary>
/// Extension methods for configuring persistence services.
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds persistence services to the service collection.
    /// </summary>
    /// <param name="services">The service collection.</param>
    /// <param name="configuration">The configuration.</param>
    /// <returns>The service collection for chaining.</returns>
    public static IServiceCollection AddPersistence(this IServiceCollection services, IConfiguration configuration)
    {
        // Add database context with fallback
        services.AddDatabaseWithFallback(configuration);

        // Add repositories
        services.AddScoped<INotificationRepository, NotificationRepository>();
        services.AddScoped<IMetricsRepository, MetricsRepository>();
        services.AddScoped<IErrorRepository, ErrorRepository>();

        // Add unit of work
        services.AddScoped<IUnitOfWork, UnitOfWork>();

        // Add database services
        services.AddSingleton<InMemoryDatabaseService>();

        return services;
    }

    /// <summary>
    /// Adds database context with fallback to in-memory database.
    /// </summary>
    /// <param name="services">The service collection.</param>
    /// <param name="configuration">The configuration.</param>
    /// <returns>The service collection for chaining.</returns>
    public static IServiceCollection AddDatabaseWithFallback(this IServiceCollection services, IConfiguration configuration)
    {
        // Try to configure main database with fallback
        try
        {
            var connectionString = configuration.GetConnectionString("DefaultConnection");
            
            if (string.IsNullOrEmpty(connectionString))
            {
                Console.WriteLine("⚠️  No database connection string found - using in-memory fallback");
                return ConfigureInMemoryFallback(services);
            }

            // Test database connection
            services.AddDbContext<NotificationDbContext>(options =>
            {
                options.UseNpgsql(connectionString);
                options.EnableSensitiveDataLogging(false);
                options.EnableServiceProviderCaching(true);
            });

            Console.WriteLine("✅ Database connection configured successfully");
            return services;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"⚠️  Database configuration failed: {ex.Message}");
            Console.WriteLine("🔄 Falling back to in-memory database");
            return ConfigureInMemoryFallback(services);
        }
    }

    /// <summary>
    /// Configures in-memory database fallback.
    /// </summary>
    /// <param name="services">The service collection.</param>
    /// <returns>The service collection for chaining.</returns>
    private static IServiceCollection ConfigureInMemoryFallback(IServiceCollection services)
    {
        // Configure in-memory database
        services.AddDbContext<NotificationDbContext>(options =>
        {
            options.UseInMemoryDatabase("NotifyMasterApi_InMemory");
            options.EnableSensitiveDataLogging(true);
        });

        // Activate the fallback service
        services.AddSingleton<IHostedService, DatabaseFallbackActivationService>();

        return services;
    }
}
