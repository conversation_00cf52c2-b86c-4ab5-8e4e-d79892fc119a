using NotifyMasterApi.Infrastructure.Entities;
using NotificationContract.Enums;

namespace NotifyMasterApi.Infrastructure.Interfaces;

/// <summary>
/// Repository interface for notification log operations.
/// Provides data access methods for notification tracking and management.
/// </summary>
public interface INotificationRepository
{
    /// <summary>
    /// Adds a new notification log entry.
    /// </summary>
    /// <param name="notificationLog">The notification log to add.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>The added notification log with generated ID.</returns>
    Task<NotificationLog> AddAsync(NotificationLog notificationLog, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Updates an existing notification log entry.
    /// </summary>
    /// <param name="notificationLog">The notification log to update.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>The updated notification log.</returns>
    Task<NotificationLog> UpdateAsync(NotificationLog notificationLog, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets a notification log by its ID.
    /// </summary>
    /// <param name="id">The notification log ID.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>The notification log if found, null otherwise.</returns>
    Task<NotificationLog?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets a notification log by its message ID.
    /// </summary>
    /// <param name="messageId">The message ID.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>The notification log if found, null otherwise.</returns>
    Task<NotificationLog?> GetByMessageIdAsync(string messageId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets notification logs with optional filtering and pagination.
    /// </summary>
    /// <param name="type">Optional notification type filter.</param>
    /// <param name="status">Optional status filter.</param>
    /// <param name="provider">Optional provider filter.</param>
    /// <param name="fromDate">Optional start date filter.</param>
    /// <param name="toDate">Optional end date filter.</param>
    /// <param name="skip">Number of records to skip for pagination.</param>
    /// <param name="take">Number of records to take for pagination.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>List of notification logs matching the criteria.</returns>
    Task<IEnumerable<NotificationLog>> GetAsync(
        NotificationType? type = null,
        NotificationStatus? status = null,
        string? provider = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        int skip = 0,
        int take = 100,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets the count of notification logs matching the specified criteria.
    /// </summary>
    /// <param name="type">Optional notification type filter.</param>
    /// <param name="status">Optional status filter.</param>
    /// <param name="provider">Optional provider filter.</param>
    /// <param name="fromDate">Optional start date filter.</param>
    /// <param name="toDate">Optional end date filter.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>The count of matching notification logs.</returns>
    Task<int> GetCountAsync(
        NotificationType? type = null,
        NotificationStatus? status = null,
        string? provider = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets notification logs that need to be retried.
    /// </summary>
    /// <param name="maxRetryCount">Maximum retry count to consider.</param>
    /// <param name="retryAfter">Only include notifications that haven't been retried within this timespan.</param>
    /// <param name="take">Maximum number of records to return.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>List of notification logs that need retry.</returns>
    Task<IEnumerable<NotificationLog>> GetForRetryAsync(
        int maxRetryCount = 3,
        TimeSpan? retryAfter = null,
        int take = 100,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Deletes old notification logs based on retention policy.
    /// </summary>
    /// <param name="olderThan">Delete logs older than this date.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>Number of deleted records.</returns>
    Task<int> DeleteOldLogsAsync(DateTime olderThan, CancellationToken cancellationToken = default);
}
