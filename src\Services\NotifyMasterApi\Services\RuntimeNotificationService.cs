using NotifyMasterApi.Interfaces;
using SmsContract.Models;
using EmailContract.Models;
using PushNotificationContract.Models;
using System.Diagnostics;
using System.Diagnostics.Metrics;

namespace NotifyMasterApi.Services;

/// <summary>
/// Service that routes notifications to runtime-loaded plugins with comprehensive error handling and monitoring.
/// </summary>
/// <remarks>
/// This service provides a high-level abstraction for sending notifications through dynamically loaded plugins.
/// It handles provider selection, failover logic, performance monitoring, and comprehensive error handling.
///
/// Key features:
/// - Automatic provider selection with preference support
/// - Failover to alternative providers on failure
/// - Performance monitoring and metrics collection
/// - Comprehensive error handling and logging
/// - Plugin capability validation
/// - Circuit breaker pattern for failing providers
/// </remarks>
public class RuntimeNotificationService
{
    private readonly ILogger<RuntimeNotificationService> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly IConfiguration _configuration;
    private readonly Meter _meter;
    private readonly Counter<long> _notificationsSentCounter;
    private readonly Counter<long> _notificationsFailedCounter;
    private readonly Histogram<double> _notificationDurationHistogram;
    private readonly Dictionary<string, DateTime> _providerFailureTracker = new();
    private readonly TimeSpan _providerCooldownPeriod;

    /// <summary>
    /// Initializes a new instance of the <see cref="RuntimeNotificationService"/> class.
    /// </summary>
    /// <param name="logger">The logger instance for this service</param>
    /// <param name="serviceProvider">The service provider for dependency resolution</param>
    /// <param name="configuration">The configuration provider for service settings</param>
    /// <exception cref="ArgumentNullException">Thrown when any required parameter is null</exception>
    public RuntimeNotificationService(
        ILogger<RuntimeNotificationService> logger,
        IServiceProvider serviceProvider,
        IConfiguration configuration)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));

        _providerCooldownPeriod = TimeSpan.FromMinutes(_configuration.GetValue<int>("RuntimeNotification:ProviderCooldownMinutes", 5));

        // Initialize metrics
        _meter = new Meter("NotificationService.Runtime");
        _notificationsSentCounter = _meter.CreateCounter<long>("runtime_notifications_sent_total",
            description: "Total number of notifications sent through runtime plugins");
        _notificationsFailedCounter = _meter.CreateCounter<long>("runtime_notifications_failed_total",
            description: "Total number of notifications that failed through runtime plugins");
        _notificationDurationHistogram = _meter.CreateHistogram<double>("runtime_notification_duration_seconds",
            description: "Duration of runtime notification processing in seconds");

        _logger.LogInformation("RuntimeNotificationService initialized with provider cooldown period: {CooldownPeriod}",
            _providerCooldownPeriod);
    }

    /// <summary>
    /// Sends an SMS message using runtime-loaded SMS plugins with failover and monitoring.
    /// </summary>
    /// <param name="request">The SMS request containing message details</param>
    /// <param name="preferredProvider">Optional preferred provider name</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>The response from the SMS provider or an error response</returns>
    /// <exception cref="ArgumentNullException">Thrown when request is null</exception>
    public async Task<object?> SendSmsAsync(SendSmsRequest request, string? preferredProvider = null, CancellationToken cancellationToken = default)
    {
        if (request == null)
            throw new ArgumentNullException(nameof(request));

        var stopwatch = Stopwatch.StartNew();
        var attemptedProviders = new List<string>();

        try
        {
            _logger.LogInformation("Sending SMS to {PhoneNumber} via runtime plugins, preferred provider: {PreferredProvider}",
                request.PhoneNumber, preferredProvider ?? "none");

            using var scope = _serviceProvider.CreateScope();
            var pluginManager = scope.ServiceProvider.GetRequiredService<IPluginManager>();

            if (pluginManager is not RuntimePluginManager runtimeManager)
            {
                var error = "Plugin manager not available or not of expected type";
                _logger.LogError(error);
                return CreateErrorResponse(error);
            }

            var smsPlugins = await runtimeManager.GetPluginsByTypeAsync("SMS");
            var availablePlugins = smsPlugins.Where(p => IsProviderAvailable(p.Provider)).ToList();

            if (!availablePlugins.Any())
            {
                var error = "No SMS plugins available or all providers are in cooldown";
                _logger.LogWarning(error);
                return CreateErrorResponse(error);
            }

            // Order plugins: preferred first, then by priority/availability
            var orderedPlugins = OrderPluginsByPreference(availablePlugins, preferredProvider);

            // Try each plugin until one succeeds
            foreach (var plugin in orderedPlugins)
            {
                attemptedProviders.Add(plugin.Provider);

                try
                {
                    _logger.LogInformation("Attempting SMS send via provider: {Provider} (plugin: {PluginName})",
                        plugin.Provider, plugin.Name);

                    using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                    timeoutCts.CancelAfter(TimeSpan.FromSeconds(30)); // 30-second timeout per provider

                    var result = await plugin.SendNotificationAsync(request);

                    stopwatch.Stop();

                    // Record success metrics
                    _notificationsSentCounter.Add(1,
                        new KeyValuePair<string, object?>("notification_type", "SMS"),
                        new KeyValuePair<string, object?>("provider", plugin.Provider));

                    _notificationDurationHistogram.Record(stopwatch.Elapsed.TotalSeconds,
                        new KeyValuePair<string, object?>("notification_type", "SMS"),
                        new KeyValuePair<string, object?>("provider", plugin.Provider),
                        new KeyValuePair<string, object?>("success", true));

                    _logger.LogInformation("SMS successfully sent via provider {Provider} in {Duration:F2}s",
                        plugin.Provider, stopwatch.Elapsed.TotalSeconds);

                    return result;
                }
                catch (OperationCanceledException) when (timeoutCts.Token.IsCancellationRequested && !cancellationToken.IsCancellationRequested)
                {
                    _logger.LogWarning("SMS send via provider {Provider} timed out after 30 seconds", plugin.Provider);
                    MarkProviderFailure(plugin.Provider);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "SMS send failed via provider {Provider}: {ErrorMessage}",
                        plugin.Provider, ex.Message);
                    MarkProviderFailure(plugin.Provider);
                }
            }

            // All providers failed
            stopwatch.Stop();
            var errorMessage = $"All SMS providers failed. Attempted providers: {string.Join(", ", attemptedProviders)}";

            _notificationsFailedCounter.Add(1,
                new KeyValuePair<string, object?>("notification_type", "SMS"),
                new KeyValuePair<string, object?>("reason", "all_providers_failed"));

            _notificationDurationHistogram.Record(stopwatch.Elapsed.TotalSeconds,
                new KeyValuePair<string, object?>("notification_type", "SMS"),
                new KeyValuePair<string, object?>("success", false));

            _logger.LogError(errorMessage);
            return CreateErrorResponse(errorMessage);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();

            _notificationsFailedCounter.Add(1,
                new KeyValuePair<string, object?>("notification_type", "SMS"),
                new KeyValuePair<string, object?>("reason", "unexpected_error"));

            _notificationDurationHistogram.Record(stopwatch.Elapsed.TotalSeconds,
                new KeyValuePair<string, object?>("notification_type", "SMS"),
                new KeyValuePair<string, object?>("success", false));

            _logger.LogError(ex, "Unexpected error sending SMS via runtime plugins");
            return CreateErrorResponse($"Unexpected error: {ex.Message}");
        }
    }

    /// <summary>
    /// Send Email using runtime-loaded Email plugins
    /// </summary>
    public async Task<object?> SendEmailAsync(SendEmailRequest request, string? preferredProvider = null)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var pluginManager = scope.ServiceProvider.GetRequiredService<IPluginManager>();

            if (pluginManager is RuntimePluginManager runtimeManager)
            {
                var emailPlugins = await runtimeManager.GetPluginsByTypeAsync("Email");
                
                // Try preferred provider first
                if (!string.IsNullOrEmpty(preferredProvider))
                {
                    var preferredPlugin = emailPlugins.FirstOrDefault(p => 
                        p.Provider.Equals(preferredProvider, StringComparison.OrdinalIgnoreCase));
                    
                    if (preferredPlugin != null)
                    {
                        _logger.LogInformation("Sending Email via preferred provider: {Provider}", preferredProvider);
                        return await preferredPlugin.SendNotificationAsync(request);
                    }
                }

                // Use first available Email plugin
                var plugin = emailPlugins.FirstOrDefault();
                if (plugin != null)
                {
                    _logger.LogInformation("Sending Email via plugin: {PluginName}", plugin.Name);
                    return await plugin.SendNotificationAsync(request);
                }
                else
                {
                    _logger.LogWarning("No Email plugins available");
                    return CreateErrorResponse("No Email plugins available");
                }
            }

            return CreateErrorResponse("Plugin manager not available");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending Email");
            return CreateErrorResponse(ex.Message);
        }
    }

    /// <summary>
    /// Send Push notification using runtime-loaded Push plugins
    /// </summary>
    public async Task<object?> SendPushAsync(SendPushMessageRequest request, string? preferredProvider = null)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var pluginManager = scope.ServiceProvider.GetRequiredService<IPluginManager>();

            if (pluginManager is RuntimePluginManager runtimeManager)
            {
                var pushPlugins = await runtimeManager.GetPluginsByTypeAsync("Push");
                
                // Try preferred provider first
                if (!string.IsNullOrEmpty(preferredProvider))
                {
                    var preferredPlugin = pushPlugins.FirstOrDefault(p => 
                        p.Provider.Equals(preferredProvider, StringComparison.OrdinalIgnoreCase));
                    
                    if (preferredPlugin != null)
                    {
                        _logger.LogInformation("Sending Push notification via preferred provider: {Provider}", preferredProvider);
                        return await preferredPlugin.SendNotificationAsync(request);
                    }
                }

                // Use first available Push plugin
                var plugin = pushPlugins.FirstOrDefault();
                if (plugin != null)
                {
                    _logger.LogInformation("Sending Push notification via plugin: {PluginName}", plugin.Name);
                    return await plugin.SendNotificationAsync(request);
                }
                else
                {
                    _logger.LogWarning("No Push plugins available");
                    return CreateErrorResponse("No Push plugins available");
                }
            }

            return CreateErrorResponse("Plugin manager not available");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending Push notification");
            return CreateErrorResponse(ex.Message);
        }
    }

    /// <summary>
    /// Get available providers for a specific notification type
    /// </summary>
    public async Task<List<string>> GetAvailableProvidersAsync(string notificationType)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var pluginManager = scope.ServiceProvider.GetRequiredService<IPluginManager>();

            if (pluginManager is RuntimePluginManager runtimeManager)
            {
                var plugins = await runtimeManager.GetPluginsByTypeAsync(notificationType);
                return plugins.Select(p => p.Provider).ToList();
            }

            return new List<string>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting available providers for {NotificationType}", notificationType);
            return new List<string>();
        }
    }

    /// <summary>
    /// Get plugin health status
    /// </summary>
    public async Task<Dictionary<string, bool>> GetPluginHealthStatusAsync()
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var pluginManager = scope.ServiceProvider.GetRequiredService<IPluginManager>();

            if (pluginManager is RuntimePluginManager runtimeManager)
            {
                return await runtimeManager.GetPluginsHealthStatusAsync();
            }

            return new Dictionary<string, bool>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting plugin health status");
            return new Dictionary<string, bool>();
        }
    }

    /// <summary>
    /// Invoke a specific method on a plugin
    /// </summary>
    public async Task<object?> InvokePluginMethodAsync(string pluginName, string methodName, params object[] parameters)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var pluginManager = scope.ServiceProvider.GetRequiredService<IPluginManager>();

            if (pluginManager is RuntimePluginManager runtimeManager)
            {
                return await runtimeManager.InvokePluginMethodAsync(pluginName, methodName, parameters);
            }

            return CreateErrorResponse("Plugin manager not available");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invoking plugin method {MethodName} on {PluginName}", methodName, pluginName);
            return CreateErrorResponse(ex.Message);
        }
    }

    /// <summary>
    /// Checks if a provider is available (not in cooldown period).
    /// </summary>
    /// <param name="providerName">The name of the provider to check</param>
    /// <returns>True if the provider is available, false if in cooldown</returns>
    private bool IsProviderAvailable(string providerName)
    {
        if (!_providerFailureTracker.TryGetValue(providerName, out var lastFailure))
            return true;

        var cooldownExpired = DateTime.UtcNow - lastFailure > _providerCooldownPeriod;
        if (cooldownExpired)
        {
            _providerFailureTracker.Remove(providerName);
            _logger.LogInformation("Provider {Provider} cooldown period expired, marking as available", providerName);
        }

        return cooldownExpired;
    }

    /// <summary>
    /// Marks a provider as failed and starts its cooldown period.
    /// </summary>
    /// <param name="providerName">The name of the provider that failed</param>
    private void MarkProviderFailure(string providerName)
    {
        _providerFailureTracker[providerName] = DateTime.UtcNow;
        _logger.LogWarning("Provider {Provider} marked as failed, entering cooldown period of {CooldownPeriod}",
            providerName, _providerCooldownPeriod);
    }

    /// <summary>
    /// Orders plugins by preference, putting the preferred provider first if available.
    /// </summary>
    /// <param name="plugins">The list of available plugins</param>
    /// <param name="preferredProvider">The preferred provider name, if any</param>
    /// <returns>An ordered list of plugins with preferred provider first</returns>
    private static List<RuntimePluginInstance> OrderPluginsByPreference(
        List<RuntimePluginInstance> plugins,
        string? preferredProvider)
    {
        if (string.IsNullOrEmpty(preferredProvider))
            return plugins;

        var preferredPlugin = plugins.FirstOrDefault(p =>
            p.Provider.Equals(preferredProvider, StringComparison.OrdinalIgnoreCase));

        if (preferredPlugin == null)
            return plugins;

        var orderedPlugins = new List<RuntimePluginInstance> { preferredPlugin };
        orderedPlugins.AddRange(plugins.Where(p => p != preferredPlugin));

        return orderedPlugins;
    }

    /// <summary>
    /// Creates a standardized error response object.
    /// </summary>
    /// <param name="errorMessage">The error message to include in the response</param>
    /// <returns>An error response object</returns>
    private static object CreateErrorResponse(string errorMessage)
    {
        return new
        {
            IsSuccess = false,
            ErrorMessage = errorMessage,
            Timestamp = DateTime.UtcNow
        };
    }

    /// <summary>
    /// Disposes of resources used by the service.
    /// </summary>
    public void Dispose()
    {
        _meter?.Dispose();
    }
}
