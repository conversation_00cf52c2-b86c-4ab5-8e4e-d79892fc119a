using PluginContract.Models;

namespace NotifyMasterApi.Interfaces;

/// <summary>
/// Defines the contract for managing runtime-loaded notification plugins.
/// </summary>
/// <remarks>
/// This interface provides comprehensive plugin lifecycle management including loading, unloading,
/// configuration management, health monitoring, and metrics collection. It supports hot-swapping
/// of plugins without service restart and provides isolation between plugin instances.
/// </remarks>
public interface IPluginManager
{
    /// <summary>
    /// Retrieves information about all currently loaded plugins.
    /// </summary>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>A collection of plugin information objects</returns>
    Task<IEnumerable<PluginInfo>> GetLoadedPluginsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Retrieves information about a specific plugin by name.
    /// </summary>
    /// <param name="name">The name of the plugin to retrieve</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>Plugin information if found, null otherwise</returns>
    Task<PluginInfo?> GetPluginAsync(string name, CancellationToken cancellationToken = default);

    /// <summary>
    /// Loads a plugin from the specified file path.
    /// </summary>
    /// <param name="pluginPath">The file path to the plugin assembly</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>True if the plugin was loaded successfully, false otherwise</returns>
    Task<bool> LoadPluginAsync(string pluginPath, CancellationToken cancellationToken = default);

    /// <summary>
    /// Unloads a plugin by name, freeing its resources.
    /// </summary>
    /// <param name="name">The name of the plugin to unload</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>True if the plugin was unloaded successfully, false otherwise</returns>
    Task<bool> UnloadPluginAsync(string name, CancellationToken cancellationToken = default);

    /// <summary>
    /// Enables a previously disabled plugin.
    /// </summary>
    /// <param name="name">The name of the plugin to enable</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>True if the plugin was enabled successfully, false otherwise</returns>
    Task<bool> EnablePluginAsync(string name, CancellationToken cancellationToken = default);

    /// <summary>
    /// Disables a plugin without unloading it.
    /// </summary>
    /// <param name="name">The name of the plugin to disable</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>True if the plugin was disabled successfully, false otherwise</returns>
    Task<bool> DisablePluginAsync(string name, CancellationToken cancellationToken = default);

    /// <summary>
    /// Retrieves the current configuration for a plugin.
    /// </summary>
    /// <param name="name">The name of the plugin</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>The plugin's configuration object</returns>
    Task<object> GetPluginConfigurationAsync(string name, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates the configuration for a plugin.
    /// </summary>
    /// <param name="name">The name of the plugin</param>
    /// <param name="configuration">The new configuration object</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>True if the configuration was updated successfully, false otherwise</returns>
    Task<bool> UpdatePluginConfigurationAsync(string name, object configuration, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates a configuration object for a plugin without applying it.
    /// </summary>
    /// <param name="name">The name of the plugin</param>
    /// <param name="configuration">The configuration object to validate</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>True if the configuration is valid, false otherwise</returns>
    Task<bool> ValidatePluginConfigurationAsync(string name, object configuration, CancellationToken cancellationToken = default);

    /// <summary>
    /// Retrieves the health status for a specific plugin.
    /// </summary>
    /// <param name="name">The name of the plugin</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>The plugin's health status information</returns>
    Task<object> GetPluginHealthStatusAsync(string name, CancellationToken cancellationToken = default);

    /// <summary>
    /// Retrieves the health status for all loaded plugins.
    /// </summary>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>A collection of health status information for all plugins</returns>
    Task<object> GetAllPluginsHealthStatusAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Retrieves performance and usage metrics for a specific plugin.
    /// </summary>
    /// <param name="name">The name of the plugin</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>The plugin's metrics information</returns>
    Task<object> GetPluginMetricsAsync(string name, CancellationToken cancellationToken = default);

    /// <summary>
    /// Uploads and installs a new plugin from a stream.
    /// </summary>
    /// <param name="pluginStream">The stream containing the plugin assembly</param>
    /// <param name="fileName">The name of the plugin file</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>True if the plugin was uploaded and installed successfully, false otherwise</returns>
    Task<bool> UploadPluginAsync(Stream pluginStream, string fileName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Deletes a plugin and removes it from the system.
    /// </summary>
    /// <param name="name">The name of the plugin to delete</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>True if the plugin was deleted successfully, false otherwise</returns>
    Task<bool> DeletePluginAsync(string name, CancellationToken cancellationToken = default);

    /// <summary>
    /// Loads all plugins found in the plugins directory.
    /// </summary>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>The number of plugins successfully loaded</returns>
    Task<int> LoadAllPluginsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Retrieves plugins filtered by notification type.
    /// </summary>
    /// <param name="notificationType">The type of notification (Email, SMS, Push)</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>A collection of plugins that support the specified notification type</returns>
    Task<IEnumerable<PluginInfo>> GetPluginsByTypeAsync(string notificationType, CancellationToken cancellationToken = default);

    /// <summary>
    /// Reloads a plugin with the latest version from disk.
    /// </summary>
    /// <param name="name">The name of the plugin to reload</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>True if the plugin was reloaded successfully, false otherwise</returns>
    Task<bool> ReloadPluginAsync(string name, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the dependency information for a plugin.
    /// </summary>
    /// <param name="name">The name of the plugin</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>Information about the plugin's dependencies</returns>
    Task<object> GetPluginDependenciesAsync(string name, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates that all plugin dependencies are satisfied.
    /// </summary>
    /// <param name="name">The name of the plugin to validate</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>True if all dependencies are satisfied, false otherwise</returns>
    Task<bool> ValidatePluginDependenciesAsync(string name, CancellationToken cancellationToken = default);
}
