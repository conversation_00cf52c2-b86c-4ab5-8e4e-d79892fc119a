using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace EmailContract.Models;

/// <summary>
/// Represents the response from an email send operation.
/// </summary>
/// <remarks>
/// This response provides information about the email sending attempt,
/// including success status, message identifiers, and any error details.
/// </remarks>
public sealed record SendEmailResponse
{
    /// <summary>
    /// Gets a value indicating whether the email was successfully sent or queued.
    /// </summary>
    [JsonPropertyName("success")]
    public required bool Success { get; init; }

    /// <summary>
    /// Gets the unique identifier for this email message.
    /// </summary>
    /// <remarks>
    /// This ID can be used to track the email status, retrieve delivery information,
    /// or reference the message in support requests.
    /// </remarks>
    /// <example>msg_1234567890abcdef</example>
    [JsonPropertyName("messageId")]
    [StringLength(100, ErrorMessage = "Message ID cannot exceed 100 characters")]
    public string? MessageId { get; init; }

    /// <summary>
    /// Gets the provider-specific message identifier.
    /// </summary>
    /// <remarks>
    /// This is the ID returned by the email service provider (e.g., SendGrid, SMTP server).
    /// It may differ from the internal MessageId and can be used for provider-specific tracking.
    /// </remarks>
    /// <example>14c5d75ce93.dfd.64b469@ismtpd-555</example>
    [JsonPropertyName("providerMessageId")]
    [StringLength(200, ErrorMessage = "Provider message ID cannot exceed 200 characters")]
    public string? ProviderMessageId { get; init; }

    /// <summary>
    /// Gets the current status of the email.
    /// </summary>
    /// <remarks>
    /// Common statuses include: Queued, Sent, Delivered, Failed, Bounced, Spam
    /// </remarks>
    /// <example>Queued</example>
    [JsonPropertyName("status")]
    [StringLength(50, ErrorMessage = "Status cannot exceed 50 characters")]
    public string? Status { get; init; }

    /// <summary>
    /// Gets the error message if the email sending failed.
    /// </summary>
    /// <example>Invalid email address format</example>
    [JsonPropertyName("errorMessage")]
    [StringLength(1000, ErrorMessage = "Error message cannot exceed 1000 characters")]
    public string? ErrorMessage { get; init; }

    /// <summary>
    /// Gets the timestamp when the email was processed.
    /// </summary>
    [JsonPropertyName("timestamp")]
    public DateTime Timestamp { get; init; } = DateTime.UtcNow;

    /// <summary>
    /// Gets the name of the provider used to send the email.
    /// </summary>
    /// <example>SendGrid</example>
    [JsonPropertyName("provider")]
    [StringLength(50, ErrorMessage = "Provider name cannot exceed 50 characters")]
    public string? Provider { get; init; }

    /// <summary>
    /// Gets additional metadata about the email sending operation.
    /// </summary>
    [JsonPropertyName("metadata")]
    public Dictionary<string, object>? Metadata { get; init; }

    /// <summary>
    /// Creates a successful email response.
    /// </summary>
    /// <param name="messageId">The message identifier</param>
    /// <param name="providerMessageId">The provider-specific message identifier</param>
    /// <param name="provider">The provider used to send the email</param>
    /// <param name="status">The current status of the email</param>
    /// <returns>A successful SendEmailResponse</returns>
    public static SendEmailResponse CreateSuccess(
        string messageId,
        string? providerMessageId = null,
        string? provider = null,
        string status = "Queued")
    {
        return new SendEmailResponse
        {
            Success = true,
            MessageId = messageId,
            ProviderMessageId = providerMessageId,
            Status = status,
            Provider = provider,
            Timestamp = DateTime.UtcNow
        };
    }

    /// <summary>
    /// Creates a failed email response.
    /// </summary>
    /// <param name="errorMessage">The error message describing the failure</param>
    /// <param name="messageId">The message identifier, if available</param>
    /// <param name="provider">The provider that was attempted</param>
    /// <returns>A failed SendEmailResponse</returns>
    public static SendEmailResponse CreateFailure(
        string errorMessage,
        string? messageId = null,
        string? provider = null)
    {
        return new SendEmailResponse
        {
            Success = false,
            MessageId = messageId,
            ErrorMessage = errorMessage,
            Status = "Failed",
            Provider = provider,
            Timestamp = DateTime.UtcNow
        };
    }
}
