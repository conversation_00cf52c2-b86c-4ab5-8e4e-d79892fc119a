using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PluginContract;
using SmsContract;
using MessageBird;
using MessageBird.Objects;
using System.Text.Json;

namespace Plugin.Sms.MessageBird;

/// <summary>
/// MessageBird SMS provider plugin with global reach and omnichannel capabilities.
/// </summary>
public class MessageBirdPlugin : ISmsProvider, IPlugin, IAdminCapable, IMetricsCapable
{
    private readonly ILogger<MessageBirdPlugin> _logger;
    private Client? _client;
    private string _accessKey = "";
    private string _defaultOriginator = "NotifyMaster";
    private bool _enableDeliveryReports = true;
    private string _messageType = "sms";
    private int _validity = 259200; // 3 days
    private string _datacoding = "auto";
    private int? _gateway;
    private readonly Dictionary<string, object> _metrics = new();
    private readonly object _metricsLock = new();

    public string Name => "MessageBird SMS Provider";
    public string Version => "1.0.0";
    public string Description => "MessageBird SMS provider with global reach and omnichannel capabilities";
    public bool IsConfigured { get; private set; }

    public MessageBirdPlugin(ILogger<MessageBirdPlugin> logger)
    {
        _logger = logger;
        InitializeMetrics();
    }

    public Task<bool> InitializeAsync(IConfiguration configuration)
    {
        try
        {
            _accessKey = configuration["accessKey"] ?? "";
            _defaultOriginator = configuration["defaultOriginator"] ?? "NotifyMaster";
            _enableDeliveryReports = bool.Parse(configuration["enableDeliveryReports"] ?? "true");
            _messageType = configuration["messageType"] ?? "sms";
            _validity = int.Parse(configuration["validity"] ?? "259200");
            _datacoding = configuration["datacoding"] ?? "auto";
            
            if (int.TryParse(configuration["gateway"], out var gateway))
            {
                _gateway = gateway;
            }

            if (string.IsNullOrEmpty(_accessKey))
            {
                _logger.LogError("MessageBird access key is required");
                return Task.FromResult(false);
            }

            _client = Client.CreateDefault(_accessKey);

            IsConfigured = true;
            _logger.LogInformation("MessageBird SMS provider initialized successfully");
            
            UpdateMetric("initialization_time", DateTime.UtcNow);
            UpdateMetric("status", "configured");
            
            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize MessageBird SMS provider");
            UpdateMetric("status", "error");
            UpdateMetric("last_error", ex.Message);
            return Task.FromResult(false);
        }
    }

    public async Task<SmsResponse> SendSmsAsync(SmsMessageRequest request)
    {
        if (!IsConfigured || _client == null)
        {
            var errorResponse = new SmsResponse
            {
                IsSuccess = false,
                ErrorMessage = "MessageBird provider is not configured",
                ProviderId = "messagebird",
                MessageId = "",
                Status = "Error"
            };
            
            UpdateMetric("total_failed", GetMetricValue<int>("total_failed") + 1);
            return errorResponse;
        }

        try
        {
            var startTime = DateTime.UtcNow;
            
            var message = new Message
            {
                Originator = request.From ?? _defaultOriginator,
                Recipients = new List<long> { long.Parse(request.PhoneNumber.Replace("+", "")) },
                Body = request.Message,
                Type = _messageType switch
                {
                    "flash" => MessageType.Flash,
                    "binary" => MessageType.Binary,
                    _ => MessageType.Sms
                },
                Validity = _validity,
                DataCoding = _datacoding switch
                {
                    "unicode" => DataCoding.Unicode,
                    "plain" => DataCoding.Plain,
                    _ => DataCoding.Auto
                }
            };

            if (_enableDeliveryReports)
            {
                message.ReportUrl = "https://your-webhook-url.com/messagebird/delivery"; // Configure as needed
            }

            if (_gateway.HasValue)
            {
                message.Gateway = _gateway.Value;
            }

            var response = await Task.Run(() => _client.SendMessage(message));
            var processingTime = (DateTime.UtcNow - startTime).TotalMilliseconds;

            var isSuccess = response != null && !string.IsNullOrEmpty(response.Id);
            
            var smsResponse = new SmsResponse
            {
                IsSuccess = isSuccess,
                MessageId = response?.Id ?? "",
                ProviderId = "messagebird",
                Status = isSuccess ? "Sent" : "Failed",
                ErrorMessage = isSuccess ? null : "Failed to send message via MessageBird",
                ProviderResponse = JsonSerializer.Serialize(response),
                SentAt = DateTime.UtcNow
            };

            // Update metrics
            UpdateMetric("total_sent", GetMetricValue<int>("total_sent") + 1);
            if (isSuccess)
            {
                UpdateMetric("total_successful", GetMetricValue<int>("total_successful") + 1);
                UpdateMetric("last_successful_send", DateTime.UtcNow);
            }
            else
            {
                UpdateMetric("total_failed", GetMetricValue<int>("total_failed") + 1);
            }
            
            UpdateMetric("average_response_time", processingTime);
            UpdateMetric("last_send_time", DateTime.UtcNow);

            _logger.LogInformation("SMS sent via MessageBird: {MessageId}, Status: {Status}", 
                smsResponse.MessageId, smsResponse.Status);

            return smsResponse;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send SMS via MessageBird");
            
            var errorResponse = new SmsResponse
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                ProviderId = "messagebird",
                MessageId = "",
                Status = "Error"
            };
            
            UpdateMetric("total_failed", GetMetricValue<int>("total_failed") + 1);
            UpdateMetric("last_error", ex.Message);
            
            return errorResponse;
        }
    }

    public async Task<bool> TestConnectionAsync()
    {
        if (!IsConfigured || _client == null)
        {
            return false;
        }

        try
        {
            // Test connection by getting balance
            var balance = await Task.Run(() => _client.Balance());
            _logger.LogInformation("MessageBird connection test successful. Balance: {Amount} {Currency}", 
                balance.Amount, balance.Currency);
            
            UpdateMetric("last_connection_test", DateTime.UtcNow);
            UpdateMetric("connection_test_result", "success");
            UpdateMetric("account_balance", balance.Amount);
            UpdateMetric("account_currency", balance.Currency);
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MessageBird connection test failed");
            UpdateMetric("connection_test_result", "failed");
            UpdateMetric("last_error", ex.Message);
            return false;
        }
    }

    public Task<Dictionary<string, object>> GetMetricsAsync()
    {
        lock (_metricsLock)
        {
            return Task.FromResult(new Dictionary<string, object>(_metrics));
        }
    }

    public Task<object> ExecuteAdminCommandAsync(string command, Dictionary<string, object> parameters)
    {
        return command.ToLowerInvariant() switch
        {
            "get_balance" => GetAccountBalanceAsync(),
            "get_pricing" => GetPricingAsync(parameters),
            "lookup_number" => LookupPhoneNumberAsync(parameters),
            "get_message_status" => GetMessageStatusAsync(parameters),
            "list_contacts" => ListContactsAsync(parameters),
            "create_contact" => CreateContactAsync(parameters),
            _ => Task.FromResult<object>(new { error = "Unknown command", available_commands = new[] { 
                "get_balance", "get_pricing", "lookup_number", "get_message_status", "list_contacts", "create_contact" } })
        };
    }

    private async Task<object> GetAccountBalanceAsync()
    {
        try
        {
            if (_client == null) return new { error = "Client not initialized" };
            
            var balance = await Task.Run(() => _client.Balance());
            return new { 
                amount = balance.Amount,
                currency = balance.Currency,
                type = balance.Type
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get MessageBird account balance");
            return new { error = ex.Message };
        }
    }

    private async Task<object> GetPricingAsync(Dictionary<string, object> parameters)
    {
        try
        {
            if (_client == null) return new { error = "Client not initialized" };
            
            var countryCode = parameters.GetValueOrDefault("country_code", "US")?.ToString() ?? "US";
            
            // MessageBird pricing would typically be retrieved from their API
            // This is a placeholder implementation
            return new { 
                country_code = countryCode,
                pricing = "Contact MessageBird for current pricing",
                note = "Pricing varies by destination and volume"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get MessageBird pricing");
            return new { error = ex.Message };
        }
    }

    private async Task<object> LookupPhoneNumberAsync(Dictionary<string, object> parameters)
    {
        try
        {
            if (_client == null) return new { error = "Client not initialized" };
            
            var phoneNumber = parameters.GetValueOrDefault("phone_number")?.ToString();
            if (string.IsNullOrEmpty(phoneNumber))
            {
                return new { error = "phone_number parameter required" };
            }

            var lookup = await Task.Run(() => _client.ViewHLR(phoneNumber));
            return new { 
                phone_number = phoneNumber,
                country_code = lookup.CountryCode,
                country_prefix = lookup.CountryPrefix,
                network = lookup.Network,
                status = lookup.Status
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to lookup phone number");
            return new { error = ex.Message };
        }
    }

    private async Task<object> GetMessageStatusAsync(Dictionary<string, object> parameters)
    {
        try
        {
            if (_client == null) return new { error = "Client not initialized" };
            
            var messageId = parameters.GetValueOrDefault("message_id")?.ToString();
            if (string.IsNullOrEmpty(messageId))
            {
                return new { error = "message_id parameter required" };
            }

            var message = await Task.Run(() => _client.ViewMessage(messageId));
            return new { 
                message_id = messageId,
                status = message.Status,
                direction = message.Direction,
                created_datetime = message.CreatedDatetime,
                recipients = message.Recipients
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get message status");
            return new { error = ex.Message };
        }
    }

    private async Task<object> ListContactsAsync(Dictionary<string, object> parameters)
    {
        try
        {
            if (_client == null) return new { error = "Client not initialized" };
            
            var limit = int.Parse(parameters.GetValueOrDefault("limit", "20")?.ToString() ?? "20");
            var offset = int.Parse(parameters.GetValueOrDefault("offset", "0")?.ToString() ?? "0");
            
            var contacts = await Task.Run(() => _client.ListContacts(limit, offset));
            return new { 
                contacts = contacts.Items,
                total_count = contacts.TotalCount,
                limit = limit,
                offset = offset
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to list contacts");
            return new { error = ex.Message };
        }
    }

    private async Task<object> CreateContactAsync(Dictionary<string, object> parameters)
    {
        try
        {
            if (_client == null) return new { error = "Client not initialized" };
            
            var phoneNumber = parameters.GetValueOrDefault("phone_number")?.ToString();
            var firstName = parameters.GetValueOrDefault("first_name")?.ToString();
            var lastName = parameters.GetValueOrDefault("last_name")?.ToString();
            
            if (string.IsNullOrEmpty(phoneNumber))
            {
                return new { error = "phone_number parameter required" };
            }

            var contact = new Contact
            {
                Msisdn = long.Parse(phoneNumber.Replace("+", "")),
                FirstName = firstName,
                LastName = lastName
            };

            var createdContact = await Task.Run(() => _client.CreateContact(contact));
            return new { 
                contact_id = createdContact.Id,
                phone_number = createdContact.Msisdn,
                first_name = createdContact.FirstName,
                last_name = createdContact.LastName,
                created_datetime = createdContact.CreatedDatetime
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create contact");
            return new { error = ex.Message };
        }
    }

    private void InitializeMetrics()
    {
        lock (_metricsLock)
        {
            _metrics["total_sent"] = 0;
            _metrics["total_successful"] = 0;
            _metrics["total_failed"] = 0;
            _metrics["average_response_time"] = 0.0;
            _metrics["status"] = "not_configured";
            _metrics["provider"] = "messagebird";
            _metrics["version"] = Version;
            _metrics["features"] = new[] { "SMS", "Unicode", "DeliveryReceipts", "GlobalCoverage", "NumberLookup" };
        }
    }

    private void UpdateMetric(string key, object value)
    {
        lock (_metricsLock)
        {
            _metrics[key] = value;
        }
    }

    private T GetMetricValue<T>(string key)
    {
        lock (_metricsLock)
        {
            return _metrics.TryGetValue(key, out var value) && value is T typedValue ? typedValue : default(T)!;
        }
    }

    public void Dispose()
    {
        _client = null;
        IsConfigured = false;
        UpdateMetric("status", "disposed");
        _logger.LogInformation("MessageBird SMS provider disposed");
    }
}
