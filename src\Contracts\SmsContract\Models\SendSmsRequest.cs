
namespace SmsContract.Models;
/// <summary>
///  Model for representing a request to send an SMS
/// </summary>
/// <param name="ReceptorPhoneNumber"></param>
/// <param name="Message"></param>
///  Model for representing a request to send an SMS
/// </summary>
/// <remarks>
/// This model represents the request to send an SMS, including:
/// - Phone number of the recipient
/// - Message of the SMS
/// </remarks>
/// <param name="ReceptorPhoneNumber"> Phone number of the recipient </param>
/// <param name="Message"> Message of the SMS </param>
public sealed record SendSmsRequest(string ReceptorPhoneNumber, string Message);
