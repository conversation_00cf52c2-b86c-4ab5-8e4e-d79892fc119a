using NotificationContract.Models;

namespace EmailService.Library.Interfaces;

public interface IEmailService
{
    // Core email functionality
    Task<EmailResponse> SendAsync(EmailMessageRequest request);
    Task<BulkEmailResponse> SendBulkAsync(BulkEmailRequest request);
    Task<MessageStatusResponse> GetMessageStatusAsync(string messageId);
    Task<MessageHistoryResponse> GetMessageHistoryAsync(string emailAddress);
    Task<EmailResponse> ResendMessageAsync(string messageId);

    // Admin functionality
    Task<object> GetServiceStatusAsync();
    Task<object> GetProvidersAsync();
    Task<ServiceResult> ConfigureProviderAsync(string provider, object configuration);
    Task<object> TestProviderAsync(string provider, string? testEmail = null);
    Task<ServiceResult> UpdateProviderStatusAsync(string provider, bool enabled);
    Task<object> GetConfigurationAsync();
    Task<ServiceResult> UpdateConfigurationAsync(object configuration);
    Task ClearCacheAsync();
    Task<object> GetQueueStatusAsync();
    Task<ServiceResult> PurgeQueueAsync();

    // Metrics functionality
    Task<object> GetSummaryMetricsAsync();
    Task<object> GetDetailedMetricsAsync(DateTime? startDate = null, DateTime? endDate = null, string? provider = null);
    Task<object> GetErrorMetricsAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<object> GetMonthlyMetricsAsync(int months = 12);
    Task<object> GetPerformanceMetricsAsync();
    Task<object> GetDeliveryRateMetricsAsync(string? provider = null);
    Task<object> GetDeliveryMetricsAsync(string? provider = null);
    Task<object> GetProviderMetricsAsync(string provider);
    Task<object> GetMonthlyStatisticsAsync();
}


