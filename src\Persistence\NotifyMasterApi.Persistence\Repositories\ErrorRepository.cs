using Microsoft.EntityFrameworkCore;
using NotifyMasterApi.Infrastructure.Entities;
using NotifyMasterApi.Infrastructure.Interfaces;
using NotifyMasterApi.Persistence.Data;
using NotificationContract.Enums;

namespace NotifyMasterApi.Persistence.Repositories;

/// <summary>
/// Repository implementation for notification error operations.
/// Provides data access methods for error tracking and management.
/// </summary>
public class ErrorRepository : IErrorRepository
{
    private readonly NotificationDbContext _context;

    /// <summary>
    /// Initializes a new instance of the ErrorRepository.
    /// </summary>
    /// <param name="context">The database context.</param>
    public ErrorRepository(NotificationDbContext context)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
    }

    /// <inheritdoc />
    public async Task<NotificationError> AddAsync(NotificationError error, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(error);
        
        error.OccurredAt = DateTime.UtcNow;
        
        var entry = await _context.NotificationErrors.AddAsync(error, cancellationToken);
        return entry.Entity;
    }

    /// <inheritdoc />
    public async Task<NotificationError> UpdateAsync(NotificationError error, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(error);
        
        var entry = _context.NotificationErrors.Update(error);
        return entry.Entity;
    }

    /// <inheritdoc />
    public async Task<NotificationError?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.NotificationErrors
            .FirstOrDefaultAsync(x => x.Id == id, cancellationToken);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<NotificationError>> GetAsync(
        NotificationType? type = null,
        string? provider = null,
        int? severity = null,
        bool? isResolved = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        int skip = 0,
        int take = 100,
        CancellationToken cancellationToken = default)
    {
        var query = _context.NotificationErrors.AsQueryable();

        if (type.HasValue)
            query = query.Where(x => x.Type == type.Value);

        if (!string.IsNullOrWhiteSpace(provider))
            query = query.Where(x => x.Provider == provider);

        if (severity.HasValue)
            query = query.Where(x => x.Severity == severity.Value);

        if (isResolved.HasValue)
            query = query.Where(x => x.IsResolved == isResolved.Value);

        if (fromDate.HasValue)
            query = query.Where(x => x.OccurredAt >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(x => x.OccurredAt <= toDate.Value);

        return await query
            .OrderByDescending(x => x.OccurredAt)
            .Skip(skip)
            .Take(take)
            .ToListAsync(cancellationToken);
    }

    /// <inheritdoc />
    public async Task<int> GetCountAsync(
        NotificationType? type = null,
        string? provider = null,
        int? severity = null,
        bool? isResolved = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.NotificationErrors.AsQueryable();

        if (type.HasValue)
            query = query.Where(x => x.Type == type.Value);

        if (!string.IsNullOrWhiteSpace(provider))
            query = query.Where(x => x.Provider == provider);

        if (severity.HasValue)
            query = query.Where(x => x.Severity == severity.Value);

        if (isResolved.HasValue)
            query = query.Where(x => x.IsResolved == isResolved.Value);

        if (fromDate.HasValue)
            query = query.Where(x => x.OccurredAt >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(x => x.OccurredAt <= toDate.Value);

        return await query.CountAsync(cancellationToken);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<NotificationError>> GetCriticalUnresolvedAsync(
        int take = 100,
        CancellationToken cancellationToken = default)
    {
        return await _context.NotificationErrors
            .Where(x => x.Severity >= 4 && !x.IsResolved)
            .OrderByDescending(x => x.OccurredAt)
            .Take(take)
            .ToListAsync(cancellationToken);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<(string Provider, int ErrorCount, int CriticalCount, int UnresolvedCount)>> GetErrorStatsByProviderAsync(
        NotificationType? type = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default)
    {
        var query = _context.NotificationErrors.AsQueryable();

        if (type.HasValue)
            query = query.Where(x => x.Type == type.Value);

        if (fromDate.HasValue)
            query = query.Where(x => x.OccurredAt >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(x => x.OccurredAt <= toDate.Value);

        return await query
            .GroupBy(x => x.Provider)
            .Select(g => new
            {
                Provider = g.Key,
                ErrorCount = g.Count(),
                CriticalCount = g.Count(x => x.Severity >= 4),
                UnresolvedCount = g.Count(x => !x.IsResolved)
            })
            .OrderByDescending(x => x.ErrorCount)
            .Select(x => ValueTuple.Create(x.Provider, x.ErrorCount, x.CriticalCount, x.UnresolvedCount))
            .ToListAsync(cancellationToken);
    }

    /// <inheritdoc />
    public async Task<bool> MarkAsResolvedAsync(
        Guid id,
        string resolvedBy,
        string? resolutionNotes = null,
        CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(resolvedBy);
        
        var error = await _context.NotificationErrors
            .FirstOrDefaultAsync(x => x.Id == id, cancellationToken);

        if (error == null)
            return false;

        error.IsResolved = true;
        error.ResolvedAt = DateTime.UtcNow;
        error.ResolvedBy = resolvedBy;
        error.ResolutionNotes = resolutionNotes;

        return true;
    }

    /// <inheritdoc />
    public async Task<int> DeleteOldErrorsAsync(DateTime olderThan, CancellationToken cancellationToken = default)
    {
        var errorsToDelete = await _context.NotificationErrors
            .Where(x => x.OccurredAt < olderThan && x.IsResolved)
            .ToListAsync(cancellationToken);

        _context.NotificationErrors.RemoveRange(errorsToDelete);
        return errorsToDelete.Count;
    }
}
