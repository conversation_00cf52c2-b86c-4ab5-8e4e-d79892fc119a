using Microsoft.Extensions.Logging;
using NotificationContract.Models;
using PluginCore.Interfaces;
using PluginCore.Models;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;

namespace Plugin.Sms.BulkSms;

public class BulkSmsPlugin : ISmsPlugin
{
    private readonly ILogger<BulkSmsPlugin> _logger;
    private readonly HttpClient _httpClient;
    private Dictionary<string, PluginConfigurationItem> _configuration = new();
    private bool _isInitialized = false;

    public BulkSmsPlugin(ILogger<BulkSmsPlugin> logger)
    {
        _logger = logger;
        _httpClient = new HttpClient();
    }

    public string Name => "BulkSMS Plugin";
    public string Version => "1.0.0";
    public string Provider => "BulkSMS";

    public async Task InitializeAsync(Dictionary<string, PluginConfigurationItem> configuration)
    {
        _configuration = configuration;

        var username = GetConfigValue<string>("username");
        var password = GetConfigValue<string>("password");

        if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
        {
            throw new InvalidOperationException("BulkSms username and password are required");
        }

        var credentials = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{username}:{password}"));
        _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", credentials);
        _httpClient.BaseAddress = new Uri("https://api.bulksms.com/v1/");

        _isInitialized = true;
        _logger.LogInformation("BulkSms plugin initialized successfully");
    }

    public async Task<SmsResponse> SendAsync(SmsMessageRequest request)
    {
        if (!_isInitialized)
            throw new InvalidOperationException("Plugin not initialized");

        try
        {
            var payload = new
            {
                to = request.PhoneNumber,
                body = request.Message,
                from = GetConfigValue<string>("from")
            };

            var json = JsonSerializer.Serialize(payload);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("messages", content);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                var result = JsonSerializer.Deserialize<BulkSmsApiResponse>(responseContent);
                _logger.LogInformation("SMS sent via BulkSMS. MessageId: {MessageId}", result?.Id);
                return new SmsResponse(true, MessageId: result?.Id ?? Guid.NewGuid().ToString());
            }
            else
            {
                _logger.LogError("BulkSms API error: {StatusCode} - {Content}", response.StatusCode, responseContent);
                return new SmsResponse(false, ErrorMessage: $"API Error: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending SMS via BulkSms");
            return new SmsResponse(false, ErrorMessage: ex.Message);
        }
    }

    public async Task<BulkSmsResponse> SendBulkAsync(BulkSmsRequest request)
    {
        var results = new List<SmsResponse>();

        foreach (var sms in request.Messages)
        {
            var result = await SendAsync(sms);
            results.Add(result);
        }

        var successCount = results.Count(r => r.IsSuccess);
        return new BulkSmsResponse(
            successCount == results.Count,
            SuccessCount: successCount,
            FailureCount: results.Count - successCount,
            Results: results
        );
    }

    public async Task<MessageStatusResponse> GetMessageStatusAsync(string messageId)
    {
        if (!_isInitialized)
            throw new InvalidOperationException("Plugin not initialized");

        try
        {
            var response = await _httpClient.GetAsync($"messages/{messageId}");
            var responseContent = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                var result = JsonSerializer.Deserialize<BulkSmsMessageStatus>(responseContent);
                return new MessageStatusResponse(true, Status: result?.Status?.Type ?? "Unknown");
            }
            else
            {
                return new MessageStatusResponse(false, ErrorMessage: $"API Error: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting message status from BulkSms");
            return new MessageStatusResponse(false, ErrorMessage: ex.Message);
        }
    }

    public async Task<MessageHistoryResponse> GetMessageHistoryAsync(string phoneNumber)
    {
        if (!_isInitialized)
            throw new InvalidOperationException("Plugin not initialized");

        try
        {
            var response = await _httpClient.GetAsync($"messages?filter=to:{phoneNumber}");
            var responseContent = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                var messages = JsonSerializer.Deserialize<List<BulkSmsApiResponse>>(responseContent) ?? new List<BulkSmsApiResponse>();
                var messageList = messages.Select(m => new
                {
                    MessageId = m.Id,
                    Body = m.Body,
                    Status = "Delivered", // BulkSMS doesn't provide detailed status in list
                    From = m.From,
                    To = m.To
                }).Cast<object>().ToList();

                return new MessageHistoryResponse(true, Messages: messageList);
            }
            else
            {
                return new MessageHistoryResponse(false, ErrorMessage: $"API Error: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting message history from BulkSms");
            return new MessageHistoryResponse(false, ErrorMessage: ex.Message);
        }
    }

    public async Task<SmsResponse> ResendMessageAsync(string messageId)
    {
        if (!_isInitialized)
            throw new InvalidOperationException("Plugin not initialized");

        try
        {
            // Get original message details
            var response = await _httpClient.GetAsync($"messages/{messageId}");
            var responseContent = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                var originalMessage = JsonSerializer.Deserialize<BulkSmsApiResponse>(responseContent);
                if (originalMessage != null)
                {
                    var request = new SmsMessageRequest
                    {
                        PhoneNumber = originalMessage.To ?? "",
                        Message = originalMessage.Body ?? ""
                    };

                    return await SendAsync(request);
                }
            }

            return new SmsResponse(false, ErrorMessage: "Could not retrieve original message");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resending message via BulkSms");
            return new SmsResponse(false, ErrorMessage: ex.Message);
        }
    }

    public async Task<bool> ValidateConfigurationAsync()
    {
        try
        {
            var username = GetConfigValue<string>("username");
            var password = GetConfigValue<string>("password");

            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                return false;

            // Test API connection
            var credentials = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{username}:{password}"));
            using var testClient = new HttpClient();
            testClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", credentials);
            testClient.BaseAddress = new Uri("https://api.bulksms.com/v1/");

            var response = await testClient.GetAsync("profile");
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }

    private T? GetConfigValue<T>(string key, T? defaultValue = default)
    {
        if (_configuration.TryGetValue(key, out var configItem))
        {
            if (configItem.DefaultValue != null)
            {
                return (T)Convert.ChangeType(configItem.DefaultValue, typeof(T));
            }
        }
        return defaultValue;
    }

    public void Dispose()
    {
        _httpClient?.Dispose();
    }




}

// Response models for BulkSms API
public class BulkSmsApiResponse
{
    public string? Id { get; set; }
    public string? Type { get; set; }
    public string? From { get; set; }
    public string? To { get; set; }
    public string? Body { get; set; }
}

public class BulkSmsMessageStatus
{
    public string? Id { get; set; }
    public BulkSmsStatus? Status { get; set; }
}

public class BulkSmsStatus
{
    public string? Type { get; set; }
    public string? Subtype { get; set; }
}
