using System.Collections.Concurrent;

namespace NotifyMasterApi.Persistence.Services;

/// <summary>
/// In-memory database service for fallback scenarios.
/// Provides basic data storage when the main database is unavailable.
/// </summary>
public class InMemoryDatabaseService
{
    private readonly ConcurrentDictionary<string, object> _inMemoryData = new();
    private readonly List<string> _operationLog = [];

    /// <summary>
    /// Gets a value indicating whether the service is using fallback mode.
    /// </summary>
    public bool IsUsingFallback { get; private set; } = true;

    /// <summary>
    /// Gets the current data count.
    /// </summary>
    public int DataCount => _inMemoryData.Count;

    /// <summary>
    /// Gets the operation log.
    /// </summary>
    public IReadOnlyList<string> OperationLog => _operationLog.AsReadOnly();

    /// <summary>
    /// Logs an operation for debugging and monitoring.
    /// </summary>
    /// <param name="operation">The operation type.</param>
    /// <param name="details">Operation details.</param>
    /// <param name="status">Operation status.</param>
    private void LogOperation(string operation, string details, string status)
    {
        var logEntry = $"[{DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}] {operation}: {details} - {status}";
        _operationLog.Add(logEntry);
        
        // Keep only the last 1000 log entries
        if (_operationLog.Count > 1000)
        {
            _operationLog.RemoveAt(0);
        }
        
        Console.WriteLine($"📝 InMemory DB: {logEntry}");
    }

    /// <summary>
    /// Stores a notification log entry.
    /// </summary>
    /// <param name="notificationId">The notification ID.</param>
    /// <param name="type">The notification type.</param>
    /// <param name="recipient">The recipient.</param>
    /// <param name="status">The status.</param>
    /// <param name="errorMessage">Optional error message.</param>
    public void StoreNotificationLog(string notificationId, string type, string recipient, string status, string? errorMessage = null)
    {
        var logData = new
        {
            Id = notificationId,
            Type = type,
            Recipient = recipient,
            Status = status,
            ErrorMessage = errorMessage,
            Timestamp = DateTime.UtcNow
        };

        _inMemoryData[$"notification_{notificationId}"] = logData;
        LogOperation("NOTIFICATION_LOG", $"Stored {type} notification to {recipient}", status);
    }

    /// <summary>
    /// Stores a plugin log entry.
    /// </summary>
    /// <param name="pluginId">The plugin ID.</param>
    /// <param name="action">The action performed.</param>
    /// <param name="status">The status.</param>
    /// <param name="details">Optional details.</param>
    public void StorePluginLog(string pluginId, string action, string status, string? details = null)
    {
        var logData = new
        {
            PluginId = pluginId,
            Action = action,
            Status = status,
            Details = details,
            Timestamp = DateTime.UtcNow
        };

        _inMemoryData[$"plugin_{pluginId}_{DateTime.UtcNow.Ticks}"] = logData;
        LogOperation("PLUGIN_LOG", $"Plugin {pluginId} {action}", status);
    }

    /// <summary>
    /// Gets notification logs.
    /// </summary>
    /// <param name="limit">Maximum number of logs to return.</param>
    /// <returns>Collection of notification logs.</returns>
    public IEnumerable<object> GetNotificationLogs(int limit = 100)
    {
        return _inMemoryData
            .Where(kvp => kvp.Key.StartsWith("notification_"))
            .Take(limit)
            .Select(kvp => kvp.Value);
    }

    /// <summary>
    /// Gets plugin logs.
    /// </summary>
    /// <param name="limit">Maximum number of logs to return.</param>
    /// <returns>Collection of plugin logs.</returns>
    public IEnumerable<object> GetPluginLogs(int limit = 100)
    {
        return _inMemoryData
            .Where(kvp => kvp.Key.StartsWith("plugin_"))
            .Take(limit)
            .Select(kvp => kvp.Value);
    }

    /// <summary>
    /// Gets all stored data.
    /// </summary>
    /// <returns>Dictionary of all stored data.</returns>
    public Dictionary<string, object> GetAllData()
    {
        return new Dictionary<string, object>(_inMemoryData);
    }

    /// <summary>
    /// Clears all stored data.
    /// </summary>
    public void ClearData()
    {
        var itemCount = _inMemoryData.Count;
        _inMemoryData.Clear();
        LogOperation("CLEAR_DATA", $"Cleared {itemCount} items from in-memory storage", "Success");
    }

    /// <summary>
    /// Tries to get data by key.
    /// </summary>
    /// <typeparam name="T">The data type.</typeparam>
    /// <param name="key">The key.</param>
    /// <param name="data">The retrieved data.</param>
    /// <returns>True if data was found, false otherwise.</returns>
    public bool TryGetData<T>(string key, out T? data) where T : class
    {
        if (_inMemoryData.TryGetValue(key, out var value) && value is T typedValue)
        {
            data = typedValue;
            return true;
        }
        
        data = null;
        return false;
    }

    /// <summary>
    /// Stores data with a specific key.
    /// </summary>
    /// <param name="key">The key.</param>
    /// <param name="data">The data to store.</param>
    public void StoreData(string key, object data)
    {
        _inMemoryData[key] = data;
        LogOperation("STORE_DATA", $"Stored data with key: {key}", "Success");
    }
}
