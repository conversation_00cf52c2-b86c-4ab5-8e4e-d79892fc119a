using EmailContract.Models;
using PluginContract.Models;
using PluginContract.Interfaces;

namespace PluginCore.Interfaces;

/// <summary>
/// Defines the contract for email notification plugins.
/// </summary>
/// <remarks>
/// This interface extends the base notification plugin interface to provide
/// email-specific functionality including single and bulk email sending,
/// message tracking, and configuration validation.
/// </remarks>
public interface IEmailPlugin : INotificationPlugin
{
    /// <summary>
    /// Sends a single email message asynchronously.
    /// </summary>
    /// <param name="request">The email request containing recipient, subject, and content information</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>A task that represents the asynchronous send operation, containing the notification response</returns>
    /// <exception cref="ArgumentNullException">Thrown when request is null</exception>
    /// <exception cref="InvalidOperationException">Thrown when the plugin is not properly configured</exception>
    Task<NotificationResponse> SendAsync(SendEmailRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Sends multiple email messages in a single batch operation asynchronously.
    /// </summary>
    /// <param name="requests">The collection of email requests to send</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>A task that represents the asynchronous bulk send operation, containing the notification response</returns>
    /// <exception cref="ArgumentNullException">Thrown when requests is null</exception>
    /// <exception cref="ArgumentException">Thrown when requests collection is empty</exception>
    /// <exception cref="InvalidOperationException">Thrown when the plugin is not properly configured</exception>
    /// <remarks>
    /// Bulk operations are more efficient for sending multiple emails and provide
    /// better error handling for individual message failures.
    /// </remarks>
    Task<NotificationResponse> SendBulkAsync(IEnumerable<SendEmailRequest> requests, CancellationToken cancellationToken = default);

    /// <summary>
    /// Retrieves the current status of a previously sent email message.
    /// </summary>
    /// <param name="messageId">The unique identifier of the message to check</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>A task that represents the asynchronous status check operation, containing the notification response with status information</returns>
    /// <exception cref="ArgumentException">Thrown when messageId is null or empty</exception>
    /// <exception cref="InvalidOperationException">Thrown when the plugin is not properly configured</exception>
    /// <remarks>
    /// Status information may include delivery status, bounce information, and timestamps.
    /// Not all email providers support detailed status tracking.
    /// </remarks>
    Task<NotificationResponse> GetMessageStatusAsync(string messageId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Retrieves the message history for a specific email address.
    /// </summary>
    /// <param name="emailAddress">The email address to retrieve history for</param>
    /// <param name="pageSize">The maximum number of messages to return (optional)</param>
    /// <param name="pageToken">Token for pagination (optional)</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>A task that represents the asynchronous history retrieval operation, containing the notification response with message history</returns>
    /// <exception cref="ArgumentException">Thrown when emailAddress is null, empty, or invalid</exception>
    /// <exception cref="InvalidOperationException">Thrown when the plugin is not properly configured</exception>
    /// <remarks>
    /// History includes sent messages, delivery status, and timestamps.
    /// Results may be paginated for large datasets.
    /// </remarks>
    Task<NotificationResponse> GetMessageHistoryAsync(string emailAddress, int? pageSize = null, string? pageToken = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Resends a previously sent email message using the same content and recipient.
    /// </summary>
    /// <param name="messageId">The unique identifier of the message to resend</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>A task that represents the asynchronous resend operation, containing the notification response</returns>
    /// <exception cref="ArgumentException">Thrown when messageId is null or empty</exception>
    /// <exception cref="InvalidOperationException">Thrown when the plugin is not properly configured or the original message cannot be found</exception>
    /// <remarks>
    /// The resent message will have a new message ID but will use the same content as the original.
    /// Not all email providers support message resending.
    /// </remarks>
    Task<NotificationResponse> ResendMessageAsync(string messageId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates the current plugin configuration to ensure it's properly set up for sending emails.
    /// </summary>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>A task that represents the asynchronous validation operation, returning true if configuration is valid</returns>
    /// <remarks>
    /// This method should verify API keys, connection settings, and any other required configuration.
    /// It may perform test API calls to validate connectivity.
    /// </remarks>
    Task<bool> ValidateConfigurationAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the maximum number of recipients supported in a single email.
    /// </summary>
    /// <returns>The maximum recipient count, or null if unlimited</returns>
    int? MaxRecipientsPerEmail { get; }

    /// <summary>
    /// Gets the maximum number of emails that can be sent in a single bulk operation.
    /// </summary>
    /// <returns>The maximum bulk size, or null if unlimited</returns>
    int? MaxBulkSize { get; }

    /// <summary>
    /// Gets a value indicating whether the provider supports email tracking and status updates.
    /// </summary>
    bool SupportsTracking { get; }

    /// <summary>
    /// Gets a value indicating whether the provider supports message resending.
    /// </summary>
    bool SupportsResending { get; }
}
