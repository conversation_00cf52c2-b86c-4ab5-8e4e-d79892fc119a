using Microsoft.AspNetCore.Mvc;
using EmailService.Library.Interfaces;
using NotifyMasterApi.Services;
using NotifyMasterApi.Interfaces;

namespace NotifyMasterApi.Controllers;

/// <summary>
/// Email service administration controller
/// </summary>
/// <remarks>
/// This controller provides endpoints for managing the email service, including:
/// - Getting service status and health information
/// - Managing email providers (add, update, delete, test)
/// - Configuring email service settings
/// </remarks>
[ApiController]
[Route("api/email/admin")]
[Produces("application/json")]
[Tags("Email Administration")]
public class EmailAdminController : ControllerBase
{
    private readonly IEmailService _emailService;
    private readonly INotificationLoggingService _loggingService;
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<EmailAdminController> _logger;

    public EmailAdminController(
        IEmailService emailService,
        INotificationLoggingService loggingService,
        IPluginManager pluginManager,
        ILogger<EmailAdminController> logger)
    {
        _emailService = emailService;
        _loggingService = loggingService;
        _pluginManager = pluginManager;
        _logger = logger;
    }

    /// <summary>
    /// Get email service status and health information
    /// </summary>
    /// <returns>Service status including provider health and configuration</returns>
    /// <response code="200">Service status retrieved successfully</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("status")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> GetServiceStatus()
    {
        try
        {
            var serviceStatus = await _emailService.GetServiceStatusAsync();
            // Mock implementation - get plugin status
            var pluginStatus = new { Name = "Email", Status = "Active", Version = "1.0.0" };
            
            return Ok(new
            {
                Service = serviceStatus,
                Plugins = pluginStatus,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting email service status");
            return StatusCode(500, new { Error = "Failed to get service status", Message = ex.Message });
        }
    }

    /// <summary>
    /// Get list of available email providers and their configurations
    /// </summary>
    /// <returns>List of email providers with their current status and settings</returns>
    /// <response code="200">Email providers retrieved successfully</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("providers")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> GetEmailProviders()
    {
        try
        {
            var providers = await _emailService.GetProvidersAsync();
            var plugins = await _pluginManager.GetLoadedPluginsAsync();
            
            return Ok(new
            {
                Providers = providers,
                LoadedPlugins = plugins,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting email providers");
            return StatusCode(500, new { Error = "Failed to get email providers", Message = ex.Message });
        }
    }

    /// <summary>
    /// Configure email provider settings
    /// </summary>
    /// <param name="provider">Provider name</param>
    /// <param name="configuration">Provider configuration settings</param>
    /// <returns>Configuration result</returns>
    /// <response code="200">Provider configured successfully</response>
    /// <response code="400">Invalid configuration</response>
    /// <response code="404">Provider not found</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("providers/{provider}/configure")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> ConfigureProvider(string provider, [FromBody] object configuration)
    {
        try
        {
            var result = await _emailService.ConfigureProviderAsync(provider, configuration);
            
            if (!result.Success)
            {
                return BadRequest(new { Error = result.ErrorMessage });
            }
            
            return Ok(new
            {
                Message = "Provider configured successfully",
                Provider = provider,
                Configuration = result.Data,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new { Error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error configuring email provider {Provider}", provider);
            return StatusCode(500, new { Error = "Failed to configure provider", Message = ex.Message });
        }
    }

    /// <summary>
    /// Test email provider connectivity and configuration
    /// </summary>
    /// <param name="provider">Provider name to test</param>
    /// <param name="testEmail">Test email address (optional)</param>
    /// <returns>Test results including connectivity and send test</returns>
    /// <response code="200">Test completed successfully</response>
    /// <response code="404">Provider not found</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("providers/{provider}/test")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> TestProvider(string provider, [FromQuery] string? testEmail = null)
    {
        try
        {
            var testResult = await _emailService.TestProviderAsync(provider, testEmail);
            
            return Ok(new
            {
                Provider = provider,
                TestResults = testResult,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new { Error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error testing email provider {Provider}", provider);
            return StatusCode(500, new { Error = "Failed to test provider", Message = ex.Message });
        }
    }

    /// <summary>
    /// Enable or disable an email provider
    /// </summary>
    /// <param name="provider">Provider name</param>
    /// <param name="enabled">Enable or disable the provider</param>
    /// <returns>Provider status update result</returns>
    /// <response code="200">Provider status updated successfully</response>
    /// <response code="404">Provider not found</response>
    /// <response code="500">Internal server error</response>
    [HttpPut("providers/{provider}/status")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> UpdateProviderStatus(string provider, [FromQuery] bool enabled)
    {
        try
        {
            var result = await _emailService.UpdateProviderStatusAsync(provider, enabled);
            
            return Ok(new
            {
                Provider = provider,
                Enabled = enabled,
                Message = $"Provider {(enabled ? "enabled" : "disabled")} successfully",
                Timestamp = DateTime.UtcNow
            });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new { Error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating email provider status {Provider}", provider);
            return StatusCode(500, new { Error = "Failed to update provider status", Message = ex.Message });
        }
    }

    /// <summary>
    /// Get email service configuration settings
    /// </summary>
    /// <returns>Current service configuration</returns>
    /// <response code="200">Configuration retrieved successfully</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("configuration")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> GetConfiguration()
    {
        try
        {
            var configuration = await _emailService.GetConfigurationAsync();
            
            return Ok(new
            {
                Configuration = configuration,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting email service configuration");
            return StatusCode(500, new { Error = "Failed to get configuration", Message = ex.Message });
        }
    }

    /// <summary>
    /// Update email service configuration settings
    /// </summary>
    /// <param name="configuration">New configuration settings</param>
    /// <returns>Configuration update result</returns>
    /// <response code="200">Configuration updated successfully</response>
    /// <response code="400">Invalid configuration</response>
    /// <response code="500">Internal server error</response>
    [HttpPut("configuration")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> UpdateConfiguration([FromBody] object configuration)
    {
        try
        {
            var result = await _emailService.UpdateConfigurationAsync(configuration);
            
            if (!result.Success)
            {
                return BadRequest(new { Error = result.ErrorMessage });
            }
            
            return Ok(new
            {
                Message = "Configuration updated successfully",
                Configuration = result.Data,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating email service configuration");
            return StatusCode(500, new { Error = "Failed to update configuration", Message = ex.Message });
        }
    }

    /// <summary>
    /// Clear email service cache and reset connections
    /// </summary>
    /// <returns>Cache clear result</returns>
    /// <response code="200">Cache cleared successfully</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("cache/clear")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> ClearCache()
    {
        try
        {
            await _emailService.ClearCacheAsync();
            
            return Ok(new
            {
                Message = "Email service cache cleared successfully",
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing email service cache");
            return StatusCode(500, new { Error = "Failed to clear cache", Message = ex.Message });
        }
    }

    /// <summary>
    /// Get email queue status and management
    /// </summary>
    /// <returns>Queue status and pending messages</returns>
    /// <response code="200">Queue status retrieved successfully</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("queue")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> GetQueueStatus()
    {
        try
        {
            var queueStatus = await _emailService.GetQueueStatusAsync();
            
            return Ok(new
            {
                QueueStatus = queueStatus,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting email queue status");
            return StatusCode(500, new { Error = "Failed to get queue status", Message = ex.Message });
        }
    }

    /// <summary>
    /// Purge email queue (remove all pending messages)
    /// </summary>
    /// <param name="confirm">Confirmation flag to prevent accidental purge</param>
    /// <returns>Queue purge result</returns>
    /// <response code="200">Queue purged successfully</response>
    /// <response code="400">Confirmation required</response>
    /// <response code="500">Internal server error</response>
    [HttpDelete("queue/purge")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> PurgeQueue([FromQuery] bool confirm = false)
    {
        try
        {
            if (!confirm)
            {
                return BadRequest(new { Error = "Confirmation required. Add ?confirm=true to purge the queue." });
            }

            var result = await _emailService.PurgeQueueAsync();
            
            return Ok(new
            {
                Message = "Email queue purged successfully",
                PurgedCount = result.PurgedCount,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error purging email queue");
            return StatusCode(500, new { Error = "Failed to purge queue", Message = ex.Message });
        }
    }
}
