using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Options;
using System.Security.Claims;
using System.Text.Encodings.Web;

namespace NotifyMasterApi.Authentication;

/// <summary>
/// Authentication handler for API key authentication.
/// </summary>
public class ApiKeyAuthenticationHandler : AuthenticationHandler<ApiKeyAuthenticationSchemeOptions>
{
    private const string ApiKeyHeaderName = "X-API-Key";
    private const string ApiKeyQueryParameter = "apikey";
    
    private readonly IApiKeyService _apiKeyService;

    public ApiKeyAuthenticationHandler(
        IOptionsMonitor<ApiKeyAuthenticationSchemeOptions> options,
        ILoggerFactory logger,
        UrlEncoder encoder,
        IApiKeyService apiKeyService)
        : base(options, logger, encoder)
    {
        _apiKeyService = apiKeyService;
    }

    protected override async Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        // Try to get API key from header first
        string? apiKey = Request.Headers[ApiKeyHeaderName].FirstOrDefault();
        
        // If not in header, try query parameter
        if (string.IsNullOrEmpty(apiKey))
        {
            apiKey = Request.Query[ApiKeyQueryParameter].FirstOrDefault();
        }

        if (string.IsNullOrEmpty(apiKey))
        {
            return AuthenticateResult.NoResult();
        }

        try
        {
            var apiKeyInfo = await _apiKeyService.ValidateApiKeyAsync(apiKey);
            if (apiKeyInfo == null)
            {
                return AuthenticateResult.Fail("Invalid API key");
            }

            var claims = new List<Claim>
            {
                new(ClaimTypes.Name, apiKeyInfo.Name),
                new(ClaimTypes.NameIdentifier, apiKeyInfo.Id),
                new("api_key_id", apiKeyInfo.Id),
                new("api_key_name", apiKeyInfo.Name)
            };

            // Add role claims
            foreach (var role in apiKeyInfo.Roles)
            {
                claims.Add(new Claim(ClaimTypes.Role, role));
            }

            // Add permission claims
            foreach (var permission in apiKeyInfo.Permissions)
            {
                claims.Add(new Claim("permission", permission));
            }

            var identity = new ClaimsIdentity(claims, Scheme.Name);
            var principal = new ClaimsPrincipal(identity);
            var ticket = new AuthenticationTicket(principal, Scheme.Name);

            return AuthenticateResult.Success(ticket);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error validating API key");
            return AuthenticateResult.Fail("API key validation failed");
        }
    }
}

/// <summary>
/// Options for API key authentication scheme.
/// </summary>
public class ApiKeyAuthenticationSchemeOptions : AuthenticationSchemeOptions
{
    public const string DefaultScheme = "ApiKey";
    public string Scheme => DefaultScheme;
    public string AuthenticationType = DefaultScheme;
}

/// <summary>
/// API key information.
/// </summary>
public class ApiKeyInfo
{
    public string Id { get; set; } = "";
    public string Name { get; set; } = "";
    public string[] Roles { get; set; } = Array.Empty<string>();
    public string[] Permissions { get; set; } = Array.Empty<string>();
    public DateTime CreatedAt { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// Service for managing API keys.
/// </summary>
public interface IApiKeyService
{
    Task<ApiKeyInfo?> ValidateApiKeyAsync(string apiKey);
    Task<ApiKeyInfo> CreateApiKeyAsync(string name, string[] roles, string[] permissions, DateTime? expiresAt = null);
    Task<bool> RevokeApiKeyAsync(string apiKeyId);
    Task<IEnumerable<ApiKeyInfo>> GetApiKeysAsync();
}

/// <summary>
/// In-memory implementation of API key service.
/// In production, this should be replaced with a database-backed implementation.
/// </summary>
public class InMemoryApiKeyService : IApiKeyService
{
    private readonly Dictionary<string, (ApiKeyInfo Info, string HashedKey)> _apiKeys = new();
    private readonly ILogger<InMemoryApiKeyService> _logger;

    public InMemoryApiKeyService(ILogger<InMemoryApiKeyService> logger)
    {
        _logger = logger;
        
        // Create default admin API key for development
        var defaultKey = "notify-master-admin-key-12345";
        var hashedKey = BCrypt.Net.BCrypt.HashPassword(defaultKey);
        var adminApiKey = new ApiKeyInfo
        {
            Id = "admin-key-1",
            Name = "Default Admin Key",
            Roles = new[] { "Admin", "User" },
            Permissions = new[] { "read", "write", "admin", "metrics" },
            CreatedAt = DateTime.UtcNow,
            IsActive = true
        };
        
        _apiKeys[defaultKey] = (adminApiKey, hashedKey);
        _logger.LogInformation("Created default admin API key: {ApiKey}", defaultKey);
    }

    public Task<ApiKeyInfo?> ValidateApiKeyAsync(string apiKey)
    {
        if (_apiKeys.TryGetValue(apiKey, out var keyData))
        {
            var (info, _) = keyData;
            
            if (!info.IsActive)
                return Task.FromResult<ApiKeyInfo?>(null);
                
            if (info.ExpiresAt.HasValue && info.ExpiresAt.Value < DateTime.UtcNow)
                return Task.FromResult<ApiKeyInfo?>(null);
                
            return Task.FromResult<ApiKeyInfo?>(info);
        }

        return Task.FromResult<ApiKeyInfo?>(null);
    }

    public Task<ApiKeyInfo> CreateApiKeyAsync(string name, string[] roles, string[] permissions, DateTime? expiresAt = null)
    {
        var apiKey = GenerateApiKey();
        var hashedKey = BCrypt.Net.BCrypt.HashPassword(apiKey);
        
        var apiKeyInfo = new ApiKeyInfo
        {
            Id = Guid.NewGuid().ToString(),
            Name = name,
            Roles = roles,
            Permissions = permissions,
            CreatedAt = DateTime.UtcNow,
            ExpiresAt = expiresAt,
            IsActive = true
        };

        _apiKeys[apiKey] = (apiKeyInfo, hashedKey);
        
        return Task.FromResult(apiKeyInfo);
    }

    public Task<bool> RevokeApiKeyAsync(string apiKeyId)
    {
        var keyToRevoke = _apiKeys.FirstOrDefault(kvp => kvp.Value.Info.Id == apiKeyId);
        if (keyToRevoke.Key != null)
        {
            var (info, hashedKey) = keyToRevoke.Value;
            info.IsActive = false;
            _apiKeys[keyToRevoke.Key] = (info, hashedKey);
            return Task.FromResult(true);
        }

        return Task.FromResult(false);
    }

    public Task<IEnumerable<ApiKeyInfo>> GetApiKeysAsync()
    {
        var apiKeys = _apiKeys.Values.Select(v => v.Info).ToList();
        return Task.FromResult<IEnumerable<ApiKeyInfo>>(apiKeys);
    }

    private static string GenerateApiKey()
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        var random = new Random();
        var result = new char[32];
        
        for (int i = 0; i < result.Length; i++)
        {
            result[i] = chars[random.Next(chars.Length)];
        }
        
        return new string(result);
    }
}
