using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PluginCore.Models;
using PluginContract.Interfaces;
using System.Reflection;
using System.Runtime.Loader;
using System.Text.Json;

namespace PluginCore.Services;

public class PluginManager : IPluginManager
{
    private readonly ILogger<PluginManager> _logger;
    private readonly Dictionary<string, LoadedPlugin> _loadedPlugins = new();
    private readonly string _pluginsDirectory;

    public PluginManager(ILogger<PluginManager> logger, string pluginsDirectory = "Plugins")
    {
        _logger = logger;
        _pluginsDirectory = pluginsDirectory;

        if (!Directory.Exists(_pluginsDirectory))
        {
            Directory.CreateDirectory(_pluginsDirectory);
        }
    }

    public async Task<bool> LoadPluginAsync(string pluginPath)
    {
        try
        {
            _logger.LogInformation("Loading plugin from {PluginPath}", pluginPath);

            PluginManifest manifest;
            Assembly assembly;
            AssemblyLoadContext loadContext;

            // Check if pluginPath is a DLL file or directory
            if (File.Exists(pluginPath) && Path.GetExtension(pluginPath).Equals(".dll", StringComparison.OrdinalIgnoreCase))
            {
                // Load single DLL with embedded manifest
                loadContext = new AssemblyLoadContext($"Plugin_{Path.GetFileNameWithoutExtension(pluginPath)}", true);
                assembly = loadContext.LoadFromAssemblyPath(pluginPath);

                // Find the embedded manifest
                var manifestResource = assembly.GetManifestResourceNames()
                    .FirstOrDefault(name => name.EndsWith("manifest.json"));

                if (manifestResource == null)
                {
                    _logger.LogError("No embedded manifest.json found in plugin: {PluginPath}", pluginPath);
                    loadContext.Unload();
                    return false;
                }

                // Read and parse embedded manifest
                using var stream = assembly.GetManifestResourceStream(manifestResource);
                using var reader = new StreamReader(stream!);
                var manifestJson = await reader.ReadToEndAsync();
                manifest = JsonSerializer.Deserialize<PluginManifest>(manifestJson);

                if (manifest == null)
                {
                    _logger.LogError("Failed to deserialize embedded manifest from {PluginPath}", pluginPath);
                    loadContext.Unload();
                    return false;
                }
            }
            else if (Directory.Exists(pluginPath))
            {
                // Load directory-based plugin (legacy support)
                var manifestPath = Path.Combine(pluginPath, "manifest.json");
                if (!File.Exists(manifestPath))
                {
                    _logger.LogError("Manifest file not found at {ManifestPath}", manifestPath);
                    return false;
                }

                var manifestJson = await File.ReadAllTextAsync(manifestPath);
                manifest = JsonSerializer.Deserialize<PluginManifest>(manifestJson);

                if (manifest == null)
                {
                    _logger.LogError("Failed to deserialize manifest from {ManifestPath}", manifestPath);
                    return false;
                }

                // Load assembly
                var assemblyPath = Path.Combine(pluginPath, manifest.AssemblyName);
                if (!File.Exists(assemblyPath))
                {
                    _logger.LogError("Assembly file not found at {AssemblyPath}", assemblyPath);
                    return false;
                }

                loadContext = new AssemblyLoadContext($"Plugin_{manifest.Type}_{manifest.Provider}", true);
                assembly = loadContext.LoadFromAssemblyPath(assemblyPath);
            }
            else
            {
                _logger.LogError("Plugin path does not exist or is not a valid DLL file: {PluginPath}", pluginPath);
                return false;
            }

            // Check if plugin is already loaded
            var pluginKey = $"{manifest.Type}.{manifest.Provider}";
            if (_loadedPlugins.ContainsKey(pluginKey))
            {
                _logger.LogWarning("Plugin {PluginKey} is already loaded", pluginKey);
                loadContext.Unload();
                return false;
            }

            // Find and instantiate the plugin
            var pluginType = assembly.GetType(manifest.EntryPoint);
            if (pluginType == null)
            {
                _logger.LogError("Entry point type {EntryPoint} not found in assembly", manifest.EntryPoint);
                loadContext.Unload();
                return false;
            }

            var pluginInstance = Activator.CreateInstance(pluginType) as INotificationPlugin;
            if (pluginInstance == null)
            {
                _logger.LogError("Failed to create instance of plugin type {PluginType}", pluginType.Name);
                loadContext.Unload();
                return false;
            }

            // Initialize plugin
            var configuration = CreateConfigurationFromManifest(manifest);
            await pluginInstance.InitializeAsync(configuration);

            // Store loaded plugin
            _loadedPlugins[pluginKey] = new LoadedPlugin
            {
                Manifest = manifest,
                Instance = pluginInstance,
                LoadContext = loadContext,
                LoadedAt = DateTime.UtcNow
            };

            _logger.LogInformation("Successfully loaded plugin {PluginKey} version {Version}",
                pluginKey, manifest.Version);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load plugin from {PluginPath}", pluginPath);
            return false;
        }
    }

    public bool UnloadPlugin(string pluginKey)
    {
        try
        {
            if (!_loadedPlugins.TryGetValue(pluginKey, out var loadedPlugin))
            {
                _logger.LogWarning("Plugin {PluginKey} is not loaded", pluginKey);
                return false;
            }

            _logger.LogInformation("Unloading plugin {PluginKey}", pluginKey);

            // Dispose plugin if it implements IDisposable
            if (loadedPlugin.Instance is IDisposable disposable)
            {
                disposable.Dispose();
            }

            // Unload assembly context
            loadedPlugin.LoadContext.Unload();

            // Remove from loaded plugins
            _loadedPlugins.Remove(pluginKey);

            _logger.LogInformation("Successfully unloaded plugin {PluginKey}", pluginKey);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to unload plugin {PluginKey}", pluginKey);
            return false;
        }
    }

    public IEnumerable<PluginInfo> GetLoadedPlugins()
    {
        return _loadedPlugins.Select(kvp => new PluginInfo
        {
            Key = kvp.Key,
            Name = kvp.Value.Manifest.Name,
            Version = kvp.Value.Manifest.Version,
            Type = kvp.Value.Manifest.Type,
            Provider = kvp.Value.Manifest.Provider,
            IsEnabled = kvp.Value.Manifest.IsEnabled,
            LoadedAt = kvp.Value.LoadedAt
        });
    }

    public T? GetPlugin<T>(string pluginKey) where T : class, INotificationPlugin
    {
        if (_loadedPlugins.TryGetValue(pluginKey, out var loadedPlugin))
        {
            return loadedPlugin.Instance as T;
        }
        return null;
    }

    public IEnumerable<T> GetPluginsByType<T>() where T : class, INotificationPlugin
    {
        return _loadedPlugins.Values
            .Select(p => p.Instance as T)
            .Where(p => p != null)
            .Cast<T>();
    }

    public async Task LoadAllPluginsAsync()
    {
        // Load plugins from directories (legacy support)
        var pluginDirectories = Directory.GetDirectories(_pluginsDirectory);
        foreach (var pluginDir in pluginDirectories)
        {
            await LoadPluginAsync(pluginDir);
        }

        // Load plugins from DLL files (optimized single-file plugins)
        var pluginFiles = Directory.GetFiles(_pluginsDirectory, "*.dll", SearchOption.AllDirectories);
        foreach (var pluginFile in pluginFiles)
        {
            await LoadPluginAsync(pluginFile);
        }
    }

    // Interface implementations required by IPluginManager
    public async Task LoadPluginsAsync()
    {
        await LoadAllPluginsAsync();
    }

    private async Task LoadPluginsFromDirectoryAsync(string directory)
    {
        // Load plugins from subdirectories (legacy support)
        var pluginDirectories = Directory.GetDirectories(directory);
        foreach (var pluginDir in pluginDirectories)
        {
            await LoadPluginAsync(pluginDir);
        }

        // Load plugins from DLL files (optimized single-file plugins)
        var pluginFiles = Directory.GetFiles(directory, "*.dll", SearchOption.AllDirectories);
        foreach (var pluginFile in pluginFiles)
        {
            await LoadPluginAsync(pluginFile);
        }
    }

    public IEnumerable<INotificationPlugin> GetAllPlugins()
    {
        return _loadedPlugins.Values.Select(p => p.Instance);
    }

    public IEnumerable<INotificationPlugin> GetPluginsByType(PluginContract.Enums.PluginType type)
    {
        return _loadedPlugins.Values
            .Where(p => string.Equals(p.Manifest.Type, type.ToString(), StringComparison.OrdinalIgnoreCase))
            .Select(p => p.Instance);
    }

    public INotificationPlugin? GetPluginByName(string name)
    {
        var plugin = _loadedPlugins.Values.FirstOrDefault(p => p.Manifest.Name == name);
        return plugin?.Instance;
    }

    public IEnumerable<INotificationPlugin> GetEnabledPluginsByType(PluginContract.Enums.PluginType type)
    {
        return _loadedPlugins.Values
            .Where(p => string.Equals(p.Manifest.Type, type.ToString(), StringComparison.OrdinalIgnoreCase) && p.Manifest.IsEnabled)
            .Select(p => p.Instance);
    }

    public async Task SetPluginEnabledAsync(string pluginName, bool enabled)
    {
        var plugin = _loadedPlugins.Values.FirstOrDefault(p => p.Manifest.Name == pluginName);
        if (plugin != null)
        {
            plugin.Manifest.IsEnabled = enabled;

            // Persist the state change to the manifest file
            try
            {
                var pluginKey = $"{plugin.Manifest.Type}.{plugin.Manifest.Provider}";
                var pluginDirectory = Path.Combine(_pluginsDirectory, pluginKey);
                var manifestPath = Path.Combine(pluginDirectory, "manifest.json");

                if (File.Exists(manifestPath))
                {
                    var manifestJson = JsonSerializer.Serialize(plugin.Manifest, new JsonSerializerOptions
                    {
                        WriteIndented = true
                    });
                    await File.WriteAllTextAsync(manifestPath, manifestJson);
                    _logger.LogInformation("Plugin {PluginName} {Status} and manifest updated", pluginName, enabled ? "enabled" : "disabled");
                }
                else
                {
                    _logger.LogWarning("Manifest file not found for plugin {PluginName} at {ManifestPath}", pluginName, manifestPath);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update manifest file for plugin {PluginName}", pluginName);
                // Don't throw - the in-memory state is still updated
            }
        }
    }

    public Dictionary<string, bool> GetPluginHealthStatus()
    {
        var healthStatus = new Dictionary<string, bool>();

        foreach (var plugin in _loadedPlugins.Values)
        {
            try
            {
                // Basic health check - plugin is loaded and enabled
                healthStatus[plugin.Manifest.Name] = plugin.Manifest.IsEnabled;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Health check failed for plugin {PluginName}", plugin.Manifest.Name);
                healthStatus[plugin.Manifest.Name] = false;
            }
        }

        return healthStatus;
    }

    public async Task<Dictionary<string, bool>> GetPluginHealthStatusAsync()
    {
        var healthStatus = new Dictionary<string, bool>();

        foreach (var plugin in _loadedPlugins.Values)
        {
            try
            {
                // Perform async health check on the plugin instance
                var isHealthy = await plugin.Instance.HealthCheckAsync();
                healthStatus[plugin.Manifest.Name] = isHealthy && plugin.Manifest.IsEnabled;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Health check failed for plugin {PluginName}", plugin.Manifest.Name);
                healthStatus[plugin.Manifest.Name] = false;
            }
        }

        return healthStatus;
    }

    public void Dispose()
    {
        foreach (var plugin in _loadedPlugins.Values)
        {
            if (plugin.Instance is IDisposable disposable)
            {
                disposable.Dispose();
            }
            plugin.LoadContext.Unload();
        }
        _loadedPlugins.Clear();
    }

    private IConfiguration CreateConfigurationFromManifest(PluginManifest manifest)
    {
        var configBuilder = new ConfigurationBuilder();

        // Convert plugin configuration items to a dictionary
        var configDict = new Dictionary<string, string?>();
        foreach (var kvp in manifest.Configuration)
        {
            if (kvp.Value.DefaultValue != null)
            {
                configDict[kvp.Key] = kvp.Value.DefaultValue.ToString() ?? string.Empty;
            }
        }

        configBuilder.AddInMemoryCollection(configDict);
        return configBuilder.Build();
    }
}

public class LoadedPlugin
{
    public PluginManifest Manifest { get; set; } = new()
    {
        Name = "Unknown",
        Version = "0.0.0",
        Type = "Unknown",
        Provider = "Unknown",
        AssemblyName = "Unknown.dll",
        EntryPoint = "Unknown.Class"
    };
    public INotificationPlugin Instance { get; set; } = null!;
    public AssemblyLoadContext LoadContext { get; set; } = null!;
    public DateTime LoadedAt { get; set; }
}

public class PluginInfo
{
    public string Key { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Provider { get; set; } = string.Empty;
    public bool IsEnabled { get; set; }
    public DateTime LoadedAt { get; set; }
}
