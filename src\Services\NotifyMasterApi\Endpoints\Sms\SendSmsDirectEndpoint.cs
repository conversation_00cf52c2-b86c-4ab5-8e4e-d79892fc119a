using FastEndpoints;
using Microsoft.AspNetCore.Authorization;
using NotifyMasterApi.Gateways;
using NotifyMasterApi.Documentation;
using SmsContract.Models;
using NotificationContract.Models;
using NotificationContract.Enums;
using System.ComponentModel.DataAnnotations;

namespace NotifyMasterApi.Endpoints.Sms;

/// <summary>
/// Send SMS directly (bypass queue) endpoint using FastEndpoints
/// </summary>
[HttpPost("/sms/send-direct"), Authorize]
public class SendSmsDirectEndpoint : Endpoint<SmsMessageRequest, ApiResponse<SmsSendResult>>
{
    private readonly ISmsGateway _smsGateway;

    public SendSmsDirectEndpoint(ISmsGateway smsGateway)
    {
        _smsGateway = smsGateway;
    }

    public override void Configure()
    {
        Post("/sms/send-direct");
        Policies("ApiKeyPolicy", "JwtPolicy");
        Summary(s =>
        {
            s.Summary = "📱 Send SMS Direct (No Queue)";
            s.Description = "Send SMS immediately without queuing for urgent/critical messages";
            s.ExampleRequest = new SmsMessageRequest
            {
                PhoneNumber = "+1234567890",
                Message = "🚨 Security Alert: Suspicious login detected from IP *************."
            };
        });
        
        Description(builder => builder
            .Accepts<SmsMessageRequest>("application/json")
            .Produces<ApiResponse<SmsSendResult>>(200, "application/json")
            .ProducesProblem(400)
            .ProducesProblem(401)
            .ProducesProblem(422)
            .ProducesProblem(429)
            .ProducesProblem(500));
    }

    public override async Task HandleAsync(SmsMessageRequest req, CancellationToken ct)
    {
        try
        {
            var result = await _smsGateway.SendSmsAsync(req);

            if (result.IsSuccess)
            {
                await SendOkAsync(new ApiResponse<SmsSendResult>
                {
                    Success = true,
                    Message = "SMS sent successfully",
                    Data = new SmsSendResult
                    {
                        MessageId = result.MessageId ?? Guid.NewGuid().ToString(),
                        Provider = _smsGateway.GetCurrentProvider() ?? "Unknown",
                        Status = "Sent",
                        SentAt = DateTime.UtcNow,
                        To = req.PhoneNumber,
                        Message = req.Message,
                        QueueId = "",
                        CorrelationId = Guid.NewGuid().ToString(),
                        SegmentCount = CalculateSegmentCount(req.Message),
                        Cost = CalculateCost(req.Message)
                    },
                    RequestId = HttpContext.TraceIdentifier,
                    Timestamp = DateTime.UtcNow
                }, ct);
            }
            else
            {
                await SendAsync(new ApiResponse<SmsSendResult>
                {
                    Success = false,
                    Message = result.ErrorMessage ?? "Failed to send SMS",
                    Data = new { Error = result.ErrorMessage },
                    RequestId = HttpContext.TraceIdentifier,
                    Timestamp = DateTime.UtcNow
                }, 500, ct);
            }
        }
        catch (Exception ex)
        {
            await SendAsync(new ApiResponse<SmsSendResult>
            {
                Success = false,
                Message = "An error occurred while sending the SMS",
                Data = new { Error = ex.Message },
                RequestId = HttpContext.TraceIdentifier,
                Timestamp = DateTime.UtcNow
            }, 500, ct);
        }
    }

    private static int CalculateSegmentCount(string message)
    {
        // Basic SMS segment calculation (160 chars per segment for GSM 7-bit)
        return (int)Math.Ceiling(message.Length / 160.0);
    }

    private static decimal CalculateCost(string message)
    {
        // Basic cost calculation (example: $0.01 per segment)
        var segments = CalculateSegmentCount(message);
        return segments * 0.01m;
    }
}
