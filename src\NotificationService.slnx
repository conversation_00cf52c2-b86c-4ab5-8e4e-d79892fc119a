<?xml version="1.0" encoding="utf-8"?>
<Solution>
  <Folder Name="Contracts">
    <Project Path="Contracts\SmsContract\SmsContract.csproj" />
    <Project Path="Contracts\EmailContract\EmailContract.csproj" />
    <Project Path="Contracts\PushNotificationContract\PushNotificationContract.csproj" />
    <Project Path="Contracts\NotificationContract\NotificationContract.csproj" />
    <Project Path="Contracts\PluginContract\PluginContract.csproj" />
  </Folder>
  
  <Folder Name="Core">
    <Project Path="Core\PluginCore\PluginCore.csproj" />
  </Folder>
  
  <Folder Name="Infrastructure">
    <Project Path="Infrastructure\NotifyMasterApi.Infrastructure\NotifyMasterApi.Infrastructure.csproj" />
  </Folder>
  
  <Folder Name="Persistence">
    <Project Path="Persistence\NotifyMasterApi.Persistence\NotifyMasterApi.Persistence.csproj" />
  </Folder>
  
  <Folder Name="Services">
    <Project Path="Services\NotifyMasterApi\NotifyMasterApi.csproj" />
  </Folder>
  
  <Folder Name="Libraries">
    <Project Path="Libraries\EmailService.Library\EmailService.Library.csproj" />
    <Project Path="Libraries\SmsService.Library\SmsService.Library.csproj" />
    <Project Path="Libraries\PushNotificationService.Library\PushNotificationService.Library.csproj" />
  </Folder>
  
  <Folder Name="Plugins">
    <Project Path="Plugins\Plugin.Email.Sendgrid\Plugin.Email.Sendgrid.csproj" />
    <Project Path="Plugins\Plugin.Sms.BulkSms\Plugin.Sms.BulkSms.csproj" />
    <Project Path="Plugins\Plugin.Sms.Clickatel\Plugin.Sms.Clickatel.csproj" />
    <Project Path="Plugins\Plugin.Sms.Twilio\Plugin.Sms.Twilio.csproj" />
    <Project Path="Plugins\Plugin.Sms.Vonage\Plugin.Sms.Vonage.csproj" />
    <Project Path="Plugins\Plugin.Sms.MessageBird\Plugin.Sms.MessageBird.csproj" />
    <Project Path="Plugins\Plugin.Push.Firebase\Plugin.Push.Firebase.csproj" />
  </Folder>
  
  <Properties>
    <Property Name="Configuration|Platform" Value="Debug|Any CPU" />
  </Properties>
</Solution>
