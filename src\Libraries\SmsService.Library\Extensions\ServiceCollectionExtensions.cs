using SmsService.Library.Interfaces;
using SmsService.Library.Services;
using Microsoft.Extensions.DependencyInjection;

namespace SmsService.Library.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddSmsService(this IServiceCollection services)
    {
        services.AddScoped<ISmsService, SmsServiceImplementation>();
        
        return services;
    }
}
