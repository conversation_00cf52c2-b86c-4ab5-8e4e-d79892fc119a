using NotifyMasterApi.Infrastructure.Entities;
using NotificationContract.Enums;

namespace NotifyMasterApi.Infrastructure.Interfaces;

/// <summary>
/// Repository interface for notification error operations.
/// Provides data access methods for error tracking and management.
/// </summary>
public interface IErrorRepository
{
    /// <summary>
    /// Adds a new error entry.
    /// </summary>
    /// <param name="error">The error to add.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>The added error with generated ID.</returns>
    Task<NotificationError> AddAsync(NotificationError error, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Updates an existing error entry.
    /// </summary>
    /// <param name="error">The error to update.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>The updated error.</returns>
    Task<NotificationError> UpdateAsync(NotificationError error, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets an error by its ID.
    /// </summary>
    /// <param name="id">The error ID.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>The error if found, null otherwise.</returns>
    Task<NotificationError?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets errors with optional filtering and pagination.
    /// </summary>
    /// <param name="type">Optional notification type filter.</param>
    /// <param name="provider">Optional provider filter.</param>
    /// <param name="severity">Optional severity filter.</param>
    /// <param name="isResolved">Optional resolution status filter.</param>
    /// <param name="fromDate">Optional start date filter.</param>
    /// <param name="toDate">Optional end date filter.</param>
    /// <param name="skip">Number of records to skip for pagination.</param>
    /// <param name="take">Number of records to take for pagination.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>List of errors matching the criteria.</returns>
    Task<IEnumerable<NotificationError>> GetAsync(
        NotificationType? type = null,
        string? provider = null,
        int? severity = null,
        bool? isResolved = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        int skip = 0,
        int take = 100,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets the count of errors matching the specified criteria.
    /// </summary>
    /// <param name="type">Optional notification type filter.</param>
    /// <param name="provider">Optional provider filter.</param>
    /// <param name="severity">Optional severity filter.</param>
    /// <param name="isResolved">Optional resolution status filter.</param>
    /// <param name="fromDate">Optional start date filter.</param>
    /// <param name="toDate">Optional end date filter.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>The count of matching errors.</returns>
    Task<int> GetCountAsync(
        NotificationType? type = null,
        string? provider = null,
        int? severity = null,
        bool? isResolved = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets unresolved critical errors.
    /// </summary>
    /// <param name="take">Maximum number of records to return.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>List of unresolved critical errors.</returns>
    Task<IEnumerable<NotificationError>> GetCriticalUnresolvedAsync(
        int take = 100,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets error statistics grouped by provider.
    /// </summary>
    /// <param name="type">Optional notification type filter.</param>
    /// <param name="fromDate">Optional start date filter.</param>
    /// <param name="toDate">Optional end date filter.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>Error statistics grouped by provider.</returns>
    Task<IEnumerable<(string Provider, int ErrorCount, int CriticalCount, int UnresolvedCount)>> GetErrorStatsByProviderAsync(
        NotificationType? type = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Marks an error as resolved.
    /// </summary>
    /// <param name="id">The error ID.</param>
    /// <param name="resolvedBy">Who resolved the error.</param>
    /// <param name="resolutionNotes">Notes about the resolution.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>True if the error was found and marked as resolved, false otherwise.</returns>
    Task<bool> MarkAsResolvedAsync(
        Guid id,
        string resolvedBy,
        string? resolutionNotes = null,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Deletes old errors based on retention policy.
    /// </summary>
    /// <param name="olderThan">Delete errors older than this date.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>Number of deleted records.</returns>
    Task<int> DeleteOldErrorsAsync(DateTime olderThan, CancellationToken cancellationToken = default);
}
