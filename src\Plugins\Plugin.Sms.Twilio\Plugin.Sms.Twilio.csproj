<Project Sdk="Microsoft.NET.Sdk">

    <Import Project="..\Plugin.Common.props" />

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Twilio" Version="7.6.0" />
        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.0" />
        <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\Contracts\SmsContract\SmsContract.csproj" />
        <ProjectReference Include="..\..\Contracts\PluginContract\PluginContract.csproj" />
        <ProjectReference Include="..\..\Core\PluginCore\PluginCore.csproj" />
    </ItemGroup>

</Project>
