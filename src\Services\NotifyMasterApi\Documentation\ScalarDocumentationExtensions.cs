using Microsoft.OpenApi.Models;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace NotifyMasterApi.Documentation;

/// <summary>
/// Enhanced documentation attributes for Scalar API documentation.
/// </summary>
public static class ScalarDocumentationExtensions
{
    /// <summary>
    /// Adds comprehensive OpenAPI documentation enhancements for Scalar.
    /// </summary>
    public static IServiceCollection AddScalarDocumentationEnhancements(this IServiceCollection services)
    {
        services.AddOpenApi(options =>
        {
            options.AddDocumentTransformer((document, context, cancellationToken) =>
            {
                // Enhanced API information
                document.Info = new OpenApiInfo
                {
                    Title = "🚀 NotifyMaster API",
                    Version = "v2.0",
                    Description = GetApiDescription(),
                    Contact = new OpenApiContact
                    {
                        Name = "NotifyMaster Support",
                        Email = "<EMAIL>",
                        Url = new Uri("https://github.com/notifymaster/support")
                    },
                    License = new OpenApiLicense
                    {
                        Name = "MIT License",
                        Url = new Uri("https://opensource.org/licenses/MIT")
                    },
                    TermsOfService = new Uri("https://notifymaster.com/terms")
                };

                // Add servers
                document.Servers = new List<OpenApiServer>
                {
                    new() { Url = "https://api.notifymaster.com", Description = "Production Server" },
                    new() { Url = "https://staging-api.notifymaster.com", Description = "Staging Server" },
                    new() { Url = "http://localhost:5000", Description = "Development Server" }
                };

                // Enhanced security schemes
                document.Components ??= new OpenApiComponents();
                document.Components.SecuritySchemes = GetSecuritySchemes();

                // Add global security requirement
                document.Security = new List<OpenApiSecurityRequirement>
                {
                    new()
                    {
                        {
                            new OpenApiSecurityScheme
                            {
                                Reference = new OpenApiReference
                                {
                                    Type = ReferenceType.SecurityScheme,
                                    Id = "Bearer"
                                }
                            },
                            Array.Empty<string>()
                        }
                    }
                };

                // Add tags with descriptions
                document.Tags = GetApiTags();

                return Task.CompletedTask;
            });
        });

        return services;
    }

    private static string GetApiDescription()
    {
        return """
        ## 🌟 Welcome to NotifyMaster API

        **The Ultimate Multi-Channel Notification Platform**

        NotifyMaster is a comprehensive, enterprise-grade notification service that supports:

        ### 📧 Email Notifications
        - **Multiple Providers**: SendGrid, SMTP, Amazon SES, Mailgun
        - **Rich Content**: HTML templates, attachments, inline images
        - **Advanced Features**: Bulk sending, scheduling, tracking

        ### 📱 SMS Notifications  
        - **Global Coverage**: Twilio, BulkSMS, Clickatel, custom providers
        - **International Support**: Multi-language, country-specific routing
        - **Smart Features**: Delivery receipts, link shortening, templates

        ### 🔔 Push Notifications
        - **Cross-Platform**: iOS (APNs), Android (FCM), Web Push
        - **Rich Notifications**: Images, actions, deep linking
        - **Targeting**: Device groups, user segments, geolocation

        ### 🔧 Advanced Features
        - **Plugin Architecture**: Runtime loading/unloading of providers
        - **Real-time Events**: WebSocket notifications for all operations
        - **Comprehensive Metrics**: Detailed analytics and reporting
        - **Admin Controls**: Provider management, configuration, monitoring
        - **Authentication**: JWT, API Keys, Basic Auth, OAuth support
        - **Database Flexibility**: PostgreSQL, SQL Server, MySQL, SQLite, In-Memory

        ### 🚀 Getting Started
        1. **Authenticate**: Use JWT tokens, API keys, or OAuth
        2. **Send Notifications**: Choose your channel and send away!
        3. **Monitor**: Track delivery, metrics, and performance
        4. **Scale**: Add providers, configure failover, optimize delivery

        ### 📊 Real-time Monitoring
        Connect to our WebSocket endpoints for live updates:
        - `/events/notifications` - Notification delivery events
        - `/events/admin` - Administrative events and alerts

        **Ready to revolutionize your notifications? Let's get started! 🎯**
        """;
    }

    private static Dictionary<string, OpenApiSecurityScheme> GetSecuritySchemes()
    {
        return new Dictionary<string, OpenApiSecurityScheme>
        {
            ["Bearer"] = new()
            {
                Type = SecuritySchemeType.Http,
                Scheme = "bearer",
                BearerFormat = "JWT",
                Description = "🔐 **JWT Bearer Authentication**\n\nProvide your JWT token in the format: `Bearer {token}`\n\n**How to get a token:**\n1. Call `/api/authentication/login` with your credentials\n2. Copy the returned token\n3. Use it in the Authorization header\n\n**Example:** `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`"
            },
            ["ApiKey"] = new()
            {
                Type = SecuritySchemeType.ApiKey,
                In = ParameterLocation.Header,
                Name = "X-API-Key",
                Description = "🔑 **API Key Authentication**\n\nProvide your API key in the `X-API-Key` header.\n\n**Default API Key for Testing:**\n`notify-master-admin-key-12345`\n\n**How to get an API key:**\n1. Authenticate with JWT\n2. Call `/api/authentication/api-keys` to create a new key\n3. Use the key in the `X-API-Key` header"
            },
            ["Basic"] = new()
            {
                Type = SecuritySchemeType.Http,
                Scheme = "basic",
                Description = "🔒 **Basic HTTP Authentication**\n\nProvide username and password using Basic authentication.\n\n**Default Credentials for Testing:**\n- Username: `admin`\n- Password: `admin123`\n\n**Format:** Base64 encoded `username:password`"
            },
            ["OAuth"] = new()
            {
                Type = SecuritySchemeType.OAuth2,
                Description = "🌐 **OAuth 2.0 Authentication**\n\nUse OAuth 2.0 for secure third-party authentication.\n\n**Supported Providers:**\n- Google\n- Microsoft\n- GitHub\n- Auth0\n- Custom OIDC providers",
                Flows = new OpenApiOAuthFlows
                {
                    AuthorizationCode = new OpenApiOAuthFlow
                    {
                        AuthorizationUrl = new Uri("https://your-oauth-provider.com/oauth/authorize"),
                        TokenUrl = new Uri("https://your-oauth-provider.com/oauth/token"),
                        Scopes = new Dictionary<string, string>
                        {
                            ["openid"] = "OpenID Connect",
                            ["profile"] = "User profile information",
                            ["email"] = "User email address"
                        }
                    }
                }
            }
        };
    }

    private static List<OpenApiTag> GetApiTags()
    {
        return new List<OpenApiTag>
        {
            new() { Name = "🔐 Authentication", Description = "User authentication, API keys, and authorization management" },
            new() { Name = "📧 Email", Description = "Email notification services with multiple provider support" },
            new() { Name = "📱 SMS", Description = "SMS messaging with global provider coverage" },
            new() { Name = "🔔 Push", Description = "Push notifications for mobile and web applications" },
            new() { Name = "🔔 Notifications", Description = "Unified multi-channel notification endpoints" },
            new() { Name = "📊 Email Metrics", Description = "Email delivery analytics and performance metrics" },
            new() { Name = "📊 SMS Metrics", Description = "SMS delivery analytics and performance metrics" },
            new() { Name = "📊 Push Metrics", Description = "Push notification analytics and performance metrics" },
            new() { Name = "⚙️ Configuration", Description = "System configuration and database setup" },
            new() { Name = "🏥 Health", Description = "System health checks and status monitoring" },
            new() { Name = "🔧 Admin", Description = "Administrative functions and system management" },
            new() { Name = "📈 System Status", Description = "Real-time system status and monitoring" }
        };
    }
}

/// <summary>
/// Custom attributes for enhanced API documentation.
/// </summary>
[AttributeUsage(AttributeTargets.Method)]
public class ApiExampleAttribute : Attribute
{
    public string Example { get; }
    public string Description { get; }

    public ApiExampleAttribute(string example, string description = "")
    {
        Example = example;
        Description = description;
    }
}

[AttributeUsage(AttributeTargets.Method)]
public class ApiResponseExampleAttribute : Attribute
{
    public int StatusCode { get; }
    public string Example { get; }
    public string Description { get; }

    public ApiResponseExampleAttribute(int statusCode, string example, string description = "")
    {
        StatusCode = statusCode;
        Example = example;
        Description = description;
    }
}

[AttributeUsage(AttributeTargets.Method)]
public class ApiPerformanceAttribute : Attribute
{
    public string AverageResponseTime { get; }
    public string RateLimit { get; }

    public ApiPerformanceAttribute(string averageResponseTime, string rateLimit = "")
    {
        AverageResponseTime = averageResponseTime;
        RateLimit = rateLimit;
    }
}

/// <summary>
/// Request/Response models with enhanced documentation.
/// </summary>
public class ApiResponse<T>
{
    [Description("Indicates if the operation was successful")]
    public bool Success { get; set; }

    [Description("Human-readable message describing the result")]
    public string Message { get; set; } = "";

    [Description("The response data")]
    public T? Data { get; set; }

    [Description("Unique identifier for tracking this request")]
    public string RequestId { get; set; } = Guid.NewGuid().ToString();

    [Description("Timestamp when the response was generated")]
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

public class PaginatedResponse<T> : ApiResponse<IEnumerable<T>>
{
    [Description("Current page number (1-based)")]
    public int Page { get; set; }

    [Description("Number of items per page")]
    public int PageSize { get; set; }

    [Description("Total number of items available")]
    public int TotalItems { get; set; }

    [Description("Total number of pages available")]
    public int TotalPages { get; set; }

    [Description("Whether there are more pages available")]
    public bool HasNextPage { get; set; }

    [Description("Whether there are previous pages available")]
    public bool HasPreviousPage { get; set; }
}
