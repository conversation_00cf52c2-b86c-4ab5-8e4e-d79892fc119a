using FastEndpoints;
using Microsoft.AspNetCore.Authorization;
using NotifyMasterApi.Gateways;
using NotifyMasterApi.Services;
using NotifyMasterApi.Events.Services;
using NotifyMasterApi.Documentation;
using PushNotificationContract.Models;
using NotificationContract.Models;
using NotificationContract.Enums;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;

namespace NotifyMasterApi.Endpoints.Push;

/// <summary>
/// Send push notification endpoint using FastEndpoints
/// </summary>
[HttpPost("/push/send"), Authorize]
public class SendPushEndpoint : Endpoint<PushMessageRequest, ApiResponse<PushSendResult>>
{
    private readonly IPushGateway _pushGateway;
    private readonly INotificationLoggingService _loggingService;
    private readonly IEventPublisher _eventPublisher;

    public SendPushEndpoint(
        IPushGateway pushGateway,
        INotificationLoggingService loggingService,
        IEventPublisher eventPublisher)
    {
        _pushGateway = pushGateway;
        _loggingService = loggingService;
        _eventPublisher = eventPublisher;
    }

    public override void Configure()
    {
        Post("/push/send");
        Policies("ApiKeyPolicy", "JwtPolicy");
        Summary(s =>
        {
            s.Summary = "📱 Send Push Notification (Queued)";
            s.Description = "Queue a push notification for delivery with automatic provider failover and retry logic";
            s.ExampleRequest = new PushMessageRequest
            {
                DeviceToken = "fcm_token_12345",
                Title = "🎉 Welcome to NotifyMaster!",
                Body = "Your account is ready. Start sending amazing notifications today!"
            };
        });
        
        Description(builder => builder
            .Accepts<PushMessageRequest>("application/json")
            .Produces<ApiResponse<PushSendResult>>(200, "application/json")
            .ProducesProblem(400)
            .ProducesProblem(401)
            .ProducesProblem(422)
            .ProducesProblem(429)
            .ProducesProblem(500));
    }

    public override async Task HandleAsync(PushMessageRequest req, CancellationToken ct)
    {
        try
        {
            var correlationId = Guid.NewGuid().ToString();

            // Log the notification
            var messageId = await _loggingService.LogNotificationAsync(
                NotificationType.PushMessage,
                req.DeviceToken,
                req.Title,
                req.Body,
                null, // UserId not available in PushMessageRequest
                correlationId);

            // Send the push notification
            var result = await _pushGateway.SendPushAsync(req);

            if (result.IsSuccess)
            {
                // Publish push sent event
                var pushEvent = new PushSentEvent
                {
                    MessageId = result.MessageId ?? messageId,
                    DeviceToken = req.DeviceToken,
                    Title = req.Title,
                    Body = req.Body,
                    Platform = "Unknown", // Platform not available in PushMessageRequest
                    Provider = _pushGateway.GetCurrentProvider() ?? "Unknown",
                    SentAt = DateTime.UtcNow,
                    CorrelationId = correlationId
                };

                await _eventPublisher.PublishAsync(pushEvent);

                await SendOkAsync(new ApiResponse<PushSendResult>
                {
                    Success = true,
                    Message = "Push notification queued successfully",
                    Data = new PushSendResult
                    {
                        MessageId = result.MessageId ?? messageId,
                        Provider = _pushGateway.GetCurrentProvider() ?? "Unknown",
                        Status = "Queued",
                        SentAt = DateTime.UtcNow,
                        DeviceToken = req.DeviceToken,
                        Title = req.Title,
                        Body = req.Body,
                        Platform = "Unknown", // Platform not available in PushMessageRequest
                        QueueId = result.MessageId ?? "", // Use MessageId as QueueId
                        CorrelationId = correlationId
                    },
                    RequestId = HttpContext.TraceIdentifier,
                    Timestamp = DateTime.UtcNow
                }, ct);
            }
            else
            {
                await SendAsync(new ApiResponse<PushSendResult>
                {
                    Success = false,
                    Message = result.ErrorMessage ?? "Failed to send push notification",
                    Data = null,
                    RequestId = HttpContext.TraceIdentifier,
                    Timestamp = DateTime.UtcNow
                }, 500, ct);
            }
        }
        catch (Exception ex)
        {
            await SendAsync(new ApiResponse<PushSendResult>
            {
                Success = false,
                Message = "An error occurred while processing the push notification",
                Data = new { Error = ex.Message },
                RequestId = HttpContext.TraceIdentifier,
                Timestamp = DateTime.UtcNow
            }, 500, ct);
        }
    }
}

/// <summary>
/// Push notification sent event for WebSocket notifications
/// </summary>
public class PushSentEvent
{
    public string MessageId { get; set; } = "";
    public string DeviceToken { get; set; } = "";
    public string Title { get; set; } = "";
    public string Body { get; set; } = "";
    public string Platform { get; set; } = "";
    public string Provider { get; set; } = "";
    public DateTime SentAt { get; set; }
    public string CorrelationId { get; set; } = "";
}

/// <summary>
/// Push notification send result model
/// </summary>
public class PushSendResult
{
    /// <summary>
    /// Unique message identifier
    /// </summary>
    [Description("Unique identifier for the push notification")]
    public string MessageId { get; set; } = "";

    /// <summary>
    /// Push notification provider used
    /// </summary>
    [Description("Push notification service provider that handled the message")]
    public string Provider { get; set; } = "";

    /// <summary>
    /// Current status of the push notification
    /// </summary>
    [Description("Current delivery status of the push notification")]
    public string Status { get; set; } = "";

    /// <summary>
    /// Timestamp when push notification was sent
    /// </summary>
    [Description("UTC timestamp when the push notification was processed")]
    public DateTime SentAt { get; set; }

    /// <summary>
    /// Device token
    /// </summary>
    [Description("Device token of the recipient")]
    public string DeviceToken { get; set; } = "";

    /// <summary>
    /// Notification title
    /// </summary>
    [Description("Title of the push notification")]
    public string Title { get; set; } = "";

    /// <summary>
    /// Notification body
    /// </summary>
    [Description("Body content of the push notification")]
    public string Body { get; set; } = "";

    /// <summary>
    /// Target platform
    /// </summary>
    [Description("Target platform (ios, android, web)")]
    public string Platform { get; set; } = "";

    /// <summary>
    /// Queue ID for tracking
    /// </summary>
    [Description("Queue identifier for tracking the push notification")]
    public string QueueId { get; set; } = "";

    /// <summary>
    /// Correlation ID for request tracking
    /// </summary>
    [Description("Correlation identifier for request tracking")]
    public string CorrelationId { get; set; } = "";
}
