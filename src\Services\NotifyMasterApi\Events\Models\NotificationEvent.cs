using NotificationContract.Enums;

namespace NotifyMasterApi.Events.Models;

/// <summary>
/// Base class for all notification events
/// </summary>
public abstract class NotificationEvent
{
    /// <summary>
    /// Unique identifier for the event
    /// </summary>
    public string EventId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// Timestamp when the event occurred
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Type of notification (Email, SMS, Push)
    /// </summary>
    public NotificationType NotificationType { get; set; }

    /// <summary>
    /// Message ID associated with the notification
    /// </summary>
    public string? MessageId { get; set; }

    /// <summary>
    /// Correlation ID for tracking related events
    /// </summary>
    public string? CorrelationId { get; set; }

    /// <summary>
    /// User ID associated with the notification
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// Additional metadata for the event
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// Event fired when a notification is queued for processing
/// </summary>
public class NotificationQueuedEvent : NotificationEvent
{
    /// <summary>
    /// Queue ID assigned to the notification
    /// </summary>
    public string QueueId { get; set; } = string.Empty;

    /// <summary>
    /// Recipient of the notification
    /// </summary>
    public string Recipient { get; set; } = string.Empty;

    /// <summary>
    /// Subject/title of the notification
    /// </summary>
    public string? Subject { get; set; }

    /// <summary>
    /// Priority level of the notification
    /// </summary>
    public int Priority { get; set; } = 1;
}

/// <summary>
/// Event fired when a notification status changes
/// </summary>
public class NotificationStatusChangedEvent : NotificationEvent
{
    /// <summary>
    /// Previous status of the notification
    /// </summary>
    public string? PreviousStatus { get; set; }

    /// <summary>
    /// Current status of the notification
    /// </summary>
    public string CurrentStatus { get; set; } = string.Empty;

    /// <summary>
    /// Provider that processed the notification
    /// </summary>
    public string? Provider { get; set; }

    /// <summary>
    /// Error message if the status indicates failure
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Response data from the provider
    /// </summary>
    public string? ResponseData { get; set; }
}

/// <summary>
/// Event fired when a notification is successfully sent
/// </summary>
public class NotificationSentEvent : NotificationEvent
{
    /// <summary>
    /// Provider that sent the notification
    /// </summary>
    public string Provider { get; set; } = string.Empty;

    /// <summary>
    /// Recipient of the notification
    /// </summary>
    public string Recipient { get; set; } = string.Empty;

    /// <summary>
    /// Response time in milliseconds
    /// </summary>
    public double ResponseTimeMs { get; set; }

    /// <summary>
    /// Provider-specific response data
    /// </summary>
    public Dictionary<string, object>? ProviderResponse { get; set; }
}

/// <summary>
/// Event fired when a notification delivery is confirmed
/// </summary>
public class NotificationDeliveredEvent : NotificationEvent
{
    /// <summary>
    /// Provider that delivered the notification
    /// </summary>
    public string Provider { get; set; } = string.Empty;

    /// <summary>
    /// Recipient of the notification
    /// </summary>
    public string Recipient { get; set; } = string.Empty;

    /// <summary>
    /// Delivery confirmation timestamp
    /// </summary>
    public DateTime DeliveredAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Delivery confirmation details
    /// </summary>
    public Dictionary<string, object>? DeliveryDetails { get; set; }
}

/// <summary>
/// Event fired when a notification fails to send
/// </summary>
public class NotificationFailedEvent : NotificationEvent
{
    /// <summary>
    /// Provider that attempted to send the notification
    /// </summary>
    public string Provider { get; set; } = string.Empty;

    /// <summary>
    /// Recipient of the notification
    /// </summary>
    public string Recipient { get; set; } = string.Empty;

    /// <summary>
    /// Error code from the provider
    /// </summary>
    public string ErrorCode { get; set; } = string.Empty;

    /// <summary>
    /// Detailed error message
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// Number of retry attempts made
    /// </summary>
    public int RetryCount { get; set; }

    /// <summary>
    /// Whether the notification will be retried
    /// </summary>
    public bool WillRetry { get; set; }

    /// <summary>
    /// Stack trace if available
    /// </summary>
    public string? StackTrace { get; set; }
}

/// <summary>
/// Event fired when a notification is being retried
/// </summary>
public class NotificationRetryEvent : NotificationEvent
{
    /// <summary>
    /// Provider attempting the retry
    /// </summary>
    public string Provider { get; set; } = string.Empty;

    /// <summary>
    /// Current retry attempt number
    /// </summary>
    public int RetryAttempt { get; set; }

    /// <summary>
    /// Maximum retry attempts allowed
    /// </summary>
    public int MaxRetries { get; set; }

    /// <summary>
    /// Reason for the retry
    /// </summary>
    public string RetryReason { get; set; } = string.Empty;

    /// <summary>
    /// Next retry scheduled time
    /// </summary>
    public DateTime? NextRetryAt { get; set; }
}
