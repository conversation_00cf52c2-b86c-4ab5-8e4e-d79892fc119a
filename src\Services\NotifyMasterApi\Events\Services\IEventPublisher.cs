using NotifyMasterApi.Events.Models;

namespace NotifyMasterApi.Events.Services;

/// <summary>
/// Interface for publishing events to WebSocket clients
/// </summary>
public interface IEventPublisher
{
    /// <summary>
    /// Publish a notification event to all subscribed clients
    /// </summary>
    /// <param name="notificationEvent">The notification event to publish</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task PublishNotificationEventAsync(NotificationEvent notificationEvent, CancellationToken cancellationToken = default);

    /// <summary>
    /// Publish an admin event to all subscribed admin clients
    /// </summary>
    /// <param name="adminEvent">The admin event to publish</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task PublishAdminEventAsync(AdminEvent adminEvent, CancellationToken cancellationToken = default);

    /// <summary>
    /// Publish a notification event to a specific user
    /// </summary>
    /// <param name="userId">User ID to send the event to</param>
    /// <param name="notificationEvent">The notification event to publish</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task PublishNotificationEventToUserAsync(string userId, NotificationEvent notificationEvent, CancellationToken cancellationToken = default);

    /// <summary>
    /// Publish a notification event to clients subscribed to a specific notification type
    /// </summary>
    /// <param name="notificationType">Type of notification (Email, SMS, Push)</param>
    /// <param name="notificationEvent">The notification event to publish</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task PublishNotificationEventToTypeAsync(string notificationType, NotificationEvent notificationEvent, CancellationToken cancellationToken = default);

    /// <summary>
    /// Publish a notification event to clients subscribed to a specific provider
    /// </summary>
    /// <param name="provider">Provider name</param>
    /// <param name="notificationEvent">The notification event to publish</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task PublishNotificationEventToProviderAsync(string provider, NotificationEvent notificationEvent, CancellationToken cancellationToken = default);

    /// <summary>
    /// Publish a notification event to clients subscribed to a specific correlation ID
    /// </summary>
    /// <param name="correlationId">Correlation ID</param>
    /// <param name="notificationEvent">The notification event to publish</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task PublishNotificationEventToCorrelationAsync(string correlationId, NotificationEvent notificationEvent, CancellationToken cancellationToken = default);

    /// <summary>
    /// Publish a system health event to admin clients
    /// </summary>
    /// <param name="healthEvent">The health event to publish</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task PublishSystemHealthEventAsync(SystemHealthChangedEvent healthEvent, CancellationToken cancellationToken = default);

    /// <summary>
    /// Publish a plugin event to admin clients
    /// </summary>
    /// <param name="pluginEvent">The plugin event to publish</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task PublishPluginEventAsync(AdminEvent pluginEvent, CancellationToken cancellationToken = default);

    /// <summary>
    /// Publish a metrics update event to admin clients
    /// </summary>
    /// <param name="metricsEvent">The metrics event to publish</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task PublishMetricsEventAsync(MetricsUpdatedEvent metricsEvent, CancellationToken cancellationToken = default);

    /// <summary>
    /// Publish a system error event to admin clients
    /// </summary>
    /// <param name="errorEvent">The error event to publish</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task PublishSystemErrorEventAsync(SystemErrorEvent errorEvent, CancellationToken cancellationToken = default);
}
