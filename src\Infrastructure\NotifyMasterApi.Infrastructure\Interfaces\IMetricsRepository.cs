using NotifyMasterApi.Infrastructure.Entities;
using NotificationContract.Enums;

namespace NotifyMasterApi.Infrastructure.Interfaces;

/// <summary>
/// Repository interface for notification metrics operations.
/// Provides data access methods for metrics tracking and reporting.
/// </summary>
public interface IMetricsRepository
{
    /// <summary>
    /// Adds a new metrics entry.
    /// </summary>
    /// <param name="metrics">The metrics to add.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>The added metrics with generated ID.</returns>
    Task<NotificationMetrics> AddAsync(NotificationMetrics metrics, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Updates an existing metrics entry.
    /// </summary>
    /// <param name="metrics">The metrics to update.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>The updated metrics.</returns>
    Task<NotificationMetrics> UpdateAsync(NotificationMetrics metrics, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets metrics by ID.
    /// </summary>
    /// <param name="id">The metrics ID.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>The metrics if found, null otherwise.</returns>
    Task<NotificationMetrics?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets metrics for a specific type, provider, and date.
    /// </summary>
    /// <param name="type">The notification type.</param>
    /// <param name="provider">The provider name.</param>
    /// <param name="date">The date.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>The metrics if found, null otherwise.</returns>
    Task<NotificationMetrics?> GetByTypeProviderDateAsync(
        NotificationType type,
        string provider,
        DateTime date,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets metrics with optional filtering and pagination.
    /// </summary>
    /// <param name="type">Optional notification type filter.</param>
    /// <param name="provider">Optional provider filter.</param>
    /// <param name="fromDate">Optional start date filter.</param>
    /// <param name="toDate">Optional end date filter.</param>
    /// <param name="skip">Number of records to skip for pagination.</param>
    /// <param name="take">Number of records to take for pagination.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>List of metrics matching the criteria.</returns>
    Task<IEnumerable<NotificationMetrics>> GetAsync(
        NotificationType? type = null,
        string? provider = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        int skip = 0,
        int take = 100,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets aggregated metrics for a date range.
    /// </summary>
    /// <param name="type">Optional notification type filter.</param>
    /// <param name="provider">Optional provider filter.</param>
    /// <param name="fromDate">Start date for aggregation.</param>
    /// <param name="toDate">End date for aggregation.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>Aggregated metrics summary.</returns>
    Task<NotificationMetrics> GetAggregatedAsync(
        NotificationType? type = null,
        string? provider = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets the top performing providers by success rate.
    /// </summary>
    /// <param name="type">Optional notification type filter.</param>
    /// <param name="fromDate">Optional start date filter.</param>
    /// <param name="toDate">Optional end date filter.</param>
    /// <param name="take">Number of top providers to return.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>List of top performing providers with their metrics.</returns>
    Task<IEnumerable<NotificationMetrics>> GetTopProvidersAsync(
        NotificationType? type = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        int take = 10,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Deletes old metrics based on retention policy.
    /// </summary>
    /// <param name="olderThan">Delete metrics older than this date.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>Number of deleted records.</returns>
    Task<int> DeleteOldMetricsAsync(DateTime olderThan, CancellationToken cancellationToken = default);
}
