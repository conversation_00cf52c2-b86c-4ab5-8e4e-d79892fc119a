using NotifyMasterApi.Ioc;
using NotifyMasterApi.Configuration;
using System.Net;
using System.Net.Sockets;

var builder = WebApplication.CreateBuilder(args);

// Configure port fallback mechanism
ConfigurePortFallback(builder);

// Run database configuration wizard if needed
await RunDatabaseWizardIfNeeded(builder.Configuration);

// Add services to the container.
builder.Services.InjectService(builder.Configuration);

var app = builder.Build();

// Configure the HTTP request pipeline.
app.ConfigurePipeline();

// Start with fallback reporting
await StartWithFallbackReporting(app);

static void ConfigurePortFallback(WebApplicationBuilder builder)
{
    var preferredPorts = new[] { 5120, 5121, 5122, 5123, 5124, 5125 };
    var availablePort = FindAvailablePort(preferredPorts);

    if (availablePort != 5120)
    {
        Console.WriteLine($"⚠️  Port 5120 is in use. Falling back to port {availablePort}");
        builder.WebHost.UseUrls($"http://localhost:{availablePort}");
    }
    else
    {
        Console.WriteLine($"✅ Using preferred port {availablePort}");
    }
}

static int FindAvailablePort(int[] preferredPorts)
{
    foreach (var port in preferredPorts)
    {
        if (IsPortAvailable(port))
            return port;
    }

    // If none of the preferred ports are available, find any available port
    var listener = new TcpListener(IPAddress.Loopback, 0);
    listener.Start();
    var availablePort = ((IPEndPoint)listener.LocalEndpoint).Port;
    listener.Stop();

    Console.WriteLine($"⚠️  All preferred ports are in use. Using random port {availablePort}");
    return availablePort;
}

static bool IsPortAvailable(int port)
{
    try
    {
        var listener = new TcpListener(IPAddress.Loopback, port);
        listener.Start();
        listener.Stop();
        return true;
    }
    catch (SocketException)
    {
        return false;
    }
}

static async Task RunDatabaseWizardIfNeeded(IConfiguration configuration)
{
    try
    {
        var wizard = new DatabaseWizard(configuration);
        await wizard.RunWizardIfNeededAsync();
    }
    catch (Exception ex)
    {
        Console.WriteLine($"⚠️  Database wizard failed: {ex.Message}");
        Console.WriteLine("Continuing with existing configuration...");
    }
}

static async Task StartWithFallbackReporting(WebApplication app)
{
    try
    {
        Console.WriteLine("🚀 Starting NotifyMasterApi...");
        await app.RunAsync();
    }
    catch (Exception ex)
    {
        Console.WriteLine($"❌ Failed to start application: {ex.Message}");
        Console.WriteLine("📋 Fallback mechanisms attempted:");
        Console.WriteLine("   - Port fallback: ✅ Implemented");
        Console.WriteLine("   - Database fallback: ✅ Implemented (check service logs)");
        Console.WriteLine("   - Redis fallback: ✅ Implemented (check service logs)");
        throw;
    }
}

// Make Program class public for testing
public partial class Program { }