using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace NotifyMasterApi.Authentication;

/// <summary>
/// Service for JWT token generation and validation.
/// </summary>
public interface IJwtAuthenticationService
{
    string GenerateToken(string userId, string username, string[] roles, string[] permissions, TimeSpan? expiry = null);
    ClaimsPrincipal? ValidateToken(string token);
    Task<AuthenticationResult> AuthenticateAsync(string username, string password);
}

/// <summary>
/// JWT authentication service implementation.
/// </summary>
public class JwtAuthenticationService : IJwtAuthenticationService
{
    private readonly IConfiguration _configuration;
    private readonly IUserService _userService;
    private readonly ILogger<JwtAuthenticationService> _logger;
    private readonly string _secretKey;
    private readonly string _issuer;
    private readonly string _audience;

    public JwtAuthenticationService(
        IConfiguration configuration,
        IUserService userService,
        ILogger<JwtAuthenticationService> logger)
    {
        _configuration = configuration;
        _userService = userService;
        _logger = logger;
        
        _secretKey = _configuration["Jwt:SecretKey"] ?? "NotifyMaster-Super-Secret-Key-That-Should-Be-Changed-In-Production-12345678901234567890";
        _issuer = _configuration["Jwt:Issuer"] ?? "NotifyMasterApi";
        _audience = _configuration["Jwt:Audience"] ?? "NotifyMasterApi";
    }

    public string GenerateToken(string userId, string username, string[] roles, string[] permissions, TimeSpan? expiry = null)
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        var key = Encoding.ASCII.GetBytes(_secretKey);
        
        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, userId),
            new(ClaimTypes.Name, username),
            new(JwtRegisteredClaimNames.Sub, userId),
            new(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            new(JwtRegisteredClaimNames.Iat, DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64)
        };

        // Add role claims
        foreach (var role in roles)
        {
            claims.Add(new Claim(ClaimTypes.Role, role));
        }

        // Add permission claims
        foreach (var permission in permissions)
        {
            claims.Add(new Claim("permission", permission));
        }

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(claims),
            Expires = DateTime.UtcNow.Add(expiry ?? TimeSpan.FromHours(24)),
            Issuer = _issuer,
            Audience = _audience,
            SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);
        return tokenHandler.WriteToken(token);
    }

    public ClaimsPrincipal? ValidateToken(string token)
    {
        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_secretKey);

            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = true,
                ValidIssuer = _issuer,
                ValidateAudience = true,
                ValidAudience = _audience,
                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero
            };

            var principal = tokenHandler.ValidateToken(token, validationParameters, out SecurityToken validatedToken);
            return principal;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Token validation failed");
            return null;
        }
    }

    public async Task<AuthenticationResult> AuthenticateAsync(string username, string password)
    {
        try
        {
            var user = await _userService.ValidateUserAsync(username, password);
            if (user == null)
            {
                return AuthenticationResult.Failed("Invalid username or password");
            }

            var token = GenerateToken(user.Id, user.Username, user.Roles, user.Permissions);
            
            return AuthenticationResult.Success(token, user);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Authentication failed for user {Username}", username);
            return AuthenticationResult.Failed("Authentication failed");
        }
    }
}

/// <summary>
/// Authentication result.
/// </summary>
public class AuthenticationResult
{
    public bool IsSuccess { get; set; }
    public string? Token { get; set; }
    public UserInfo? User { get; set; }
    public string? ErrorMessage { get; set; }

    public static AuthenticationResult Success(string token, UserInfo user)
    {
        return new AuthenticationResult
        {
            IsSuccess = true,
            Token = token,
            User = user
        };
    }

    public static AuthenticationResult Failed(string errorMessage)
    {
        return new AuthenticationResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage
        };
    }
}

/// <summary>
/// User information.
/// </summary>
public class UserInfo
{
    public string Id { get; set; } = "";
    public string Username { get; set; } = "";
    public string Email { get; set; } = "";
    public string[] Roles { get; set; } = Array.Empty<string>();
    public string[] Permissions { get; set; } = Array.Empty<string>();
    public DateTime CreatedAt { get; set; }
    public DateTime? LastLoginAt { get; set; }
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// Service for managing users.
/// </summary>
public interface IUserService
{
    Task<UserInfo?> ValidateUserAsync(string username, string password);
    Task<UserInfo?> GetUserByIdAsync(string userId);
    Task<UserInfo?> GetUserByUsernameAsync(string username);
    Task<UserInfo> CreateUserAsync(string username, string email, string password, string[] roles, string[] permissions);
    Task<bool> UpdateUserAsync(UserInfo user);
    Task<bool> DeleteUserAsync(string userId);
    Task<IEnumerable<UserInfo>> GetUsersAsync();
}

/// <summary>
/// In-memory implementation of user service.
/// In production, this should be replaced with a database-backed implementation.
/// </summary>
public class InMemoryUserService : IUserService
{
    private readonly Dictionary<string, (UserInfo User, string HashedPassword)> _users = new();
    private readonly ILogger<InMemoryUserService> _logger;

    public InMemoryUserService(ILogger<InMemoryUserService> logger)
    {
        _logger = logger;
        
        // Create default admin user
        var adminUser = new UserInfo
        {
            Id = "admin-user-1",
            Username = "admin",
            Email = "<EMAIL>",
            Roles = new[] { "Admin", "User" },
            Permissions = new[] { "read", "write", "admin", "metrics" },
            CreatedAt = DateTime.UtcNow,
            IsActive = true
        };
        
        var hashedPassword = BCrypt.Net.BCrypt.HashPassword("admin123");
        _users["admin"] = (adminUser, hashedPassword);
        
        _logger.LogInformation("Created default admin user: admin/admin123");
    }

    public Task<UserInfo?> ValidateUserAsync(string username, string password)
    {
        if (_users.TryGetValue(username.ToLowerInvariant(), out var userData))
        {
            var (user, hashedPassword) = userData;
            
            if (!user.IsActive)
                return Task.FromResult<UserInfo?>(null);
                
            if (BCrypt.Net.BCrypt.Verify(password, hashedPassword))
            {
                user.LastLoginAt = DateTime.UtcNow;
                return Task.FromResult<UserInfo?>(user);
            }
        }

        return Task.FromResult<UserInfo?>(null);
    }

    public Task<UserInfo?> GetUserByIdAsync(string userId)
    {
        var user = _users.Values.FirstOrDefault(u => u.User.Id == userId)?.User;
        return Task.FromResult(user);
    }

    public Task<UserInfo?> GetUserByUsernameAsync(string username)
    {
        if (_users.TryGetValue(username.ToLowerInvariant(), out var userData))
        {
            return Task.FromResult<UserInfo?>(userData.User);
        }
        return Task.FromResult<UserInfo?>(null);
    }

    public Task<UserInfo> CreateUserAsync(string username, string email, string password, string[] roles, string[] permissions)
    {
        var user = new UserInfo
        {
            Id = Guid.NewGuid().ToString(),
            Username = username,
            Email = email,
            Roles = roles,
            Permissions = permissions,
            CreatedAt = DateTime.UtcNow,
            IsActive = true
        };

        var hashedPassword = BCrypt.Net.BCrypt.HashPassword(password);
        _users[username.ToLowerInvariant()] = (user, hashedPassword);

        return Task.FromResult(user);
    }

    public Task<bool> UpdateUserAsync(UserInfo user)
    {
        var existingUser = _users.FirstOrDefault(kvp => kvp.Value.User.Id == user.Id);
        if (existingUser.Key != null)
        {
            var (_, hashedPassword) = existingUser.Value;
            _users[existingUser.Key] = (user, hashedPassword);
            return Task.FromResult(true);
        }
        return Task.FromResult(false);
    }

    public Task<bool> DeleteUserAsync(string userId)
    {
        var userToDelete = _users.FirstOrDefault(kvp => kvp.Value.User.Id == userId);
        if (userToDelete.Key != null)
        {
            _users.Remove(userToDelete.Key);
            return Task.FromResult(true);
        }
        return Task.FromResult(false);
    }

    public Task<IEnumerable<UserInfo>> GetUsersAsync()
    {
        var users = _users.Values.Select(v => v.User).ToList();
        return Task.FromResult<IEnumerable<UserInfo>>(users);
    }
}
