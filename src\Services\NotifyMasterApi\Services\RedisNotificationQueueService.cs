using Newtonsoft.Json;
using StackExchange.Redis;

namespace NotifyMasterApi.Services;

public class RedisNotificationQueueService : INotificationQueueService
{
    private readonly IDatabase _database;
    private readonly ILogger<RedisNotificationQueueService> _logger;
    private const string QUEUE_KEY = "notification:queue";
    private const string PROCESSING_KEY = "notification:processing";
    private const string FAILED_KEY = "notification:failed";
    private const string COMPLETED_KEY = "notification:completed";

    public RedisNotificationQueueService(IConnectionMultiplexer redis, ILogger<RedisNotificationQueueService> logger)
    {
        _database = redis.GetDatabase();
        _logger = logger;
    }

    public async Task<string> QueueNotificationAsync(NotificationQueueItem item)
    {
        try
        {
            item.QueueId = Guid.NewGuid().ToString();
            item.QueuedAt = DateTime.UtcNow;

            var json = JsonConvert.SerializeObject(item);
            
            // Add to priority queue based on priority and scheduled time
            var score = CalculateScore(item);
            await _database.SortedSetAddAsync(QUEUE_KEY, json, score);

            _logger.LogInformation("Queued notification {QueueId} for {Type} to {Recipient}", 
                item.QueueId, item.Type, item.Recipient);

            return item.QueueId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error queuing notification for {Type} to {Recipient}", 
                item.Type, item.Recipient);
            throw;
        }
    }

    public async Task<NotificationQueueItem?> DequeueNotificationAsync()
    {
        try
        {
            // Get the highest priority item that's ready to be processed
            var now = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            var items = await _database.SortedSetRangeByScoreAsync(QUEUE_KEY, 0, now, Exclude.None, Order.Ascending, 0, 1);
            
            if (items.Length == 0)
                return null;

            var json = items[0];
            var item = JsonConvert.DeserializeObject<NotificationQueueItem>(json!);
            
            if (item == null)
                return null;

            // Move from queue to processing
            var transaction = _database.CreateTransaction();
            _ = transaction.SortedSetRemoveAsync(QUEUE_KEY, json);
            _ = transaction.HashSetAsync(PROCESSING_KEY, item.QueueId, json);
            
            if (await transaction.ExecuteAsync())
            {
                _logger.LogInformation("Dequeued notification {QueueId} for processing", item.QueueId);
                return item;
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error dequeuing notification");
            return null;
        }
    }

    public async Task<List<NotificationQueueItem>> GetPendingNotificationsAsync(int count = 10)
    {
        try
        {
            var items = await _database.SortedSetRangeByScoreAsync(QUEUE_KEY, 0, double.PositiveInfinity, Exclude.None, Order.Ascending, 0, count);
            var notifications = new List<NotificationQueueItem>();

            foreach (var item in items)
            {
                var notification = JsonConvert.DeserializeObject<NotificationQueueItem>(item!);
                if (notification != null)
                    notifications.Add(notification);
            }

            return notifications;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting pending notifications");
            return new List<NotificationQueueItem>();
        }
    }

    public async Task MarkNotificationProcessedAsync(string queueId)
    {
        try
        {
            var json = await _database.HashGetAsync(PROCESSING_KEY, queueId);
            if (json.HasValue)
            {
                var item = JsonConvert.DeserializeObject<NotificationQueueItem>(json!);
                if (item != null)
                {
                    item.ProcessedAt = DateTime.UtcNow;
                    var updatedJson = JsonConvert.SerializeObject(item);

                    var transaction = _database.CreateTransaction();
                    _ = transaction.HashDeleteAsync(PROCESSING_KEY, queueId);
                    _ = transaction.HashSetAsync(COMPLETED_KEY, queueId, updatedJson);
                    
                    await transaction.ExecuteAsync();
                    _logger.LogInformation("Marked notification {QueueId} as processed", queueId);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error marking notification {QueueId} as processed", queueId);
        }
    }

    public async Task MarkNotificationFailedAsync(string queueId, string errorMessage, int retryCount = 0)
    {
        try
        {
            var json = await _database.HashGetAsync(PROCESSING_KEY, queueId);
            if (json.HasValue)
            {
                var item = JsonConvert.DeserializeObject<NotificationQueueItem>(json!);
                if (item != null)
                {
                    item.RetryCount = retryCount;
                    item.ErrorMessage = errorMessage;

                    var transaction = _database.CreateTransaction();
                    _ = transaction.HashDeleteAsync(PROCESSING_KEY, queueId);

                    if (retryCount < item.MaxRetries)
                    {
                        // Requeue with exponential backoff
                        var delay = TimeSpan.FromMinutes(Math.Pow(2, retryCount));
                        item.ScheduledFor = DateTime.UtcNow.Add(delay);
                        var score = CalculateScore(item);
                        var updatedJson = JsonConvert.SerializeObject(item);
                        _ = transaction.SortedSetAddAsync(QUEUE_KEY, updatedJson, score);
                        _logger.LogWarning("Requeuing notification {QueueId} for retry {RetryCount}/{MaxRetries}", 
                            queueId, retryCount + 1, item.MaxRetries);
                    }
                    else
                    {
                        // Move to failed queue
                        var failedJson = JsonConvert.SerializeObject(item);
                        _ = transaction.HashSetAsync(FAILED_KEY, queueId, failedJson);
                        _logger.LogError("Notification {QueueId} failed permanently after {RetryCount} retries: {ErrorMessage}", 
                            queueId, retryCount, errorMessage);
                    }

                    await transaction.ExecuteAsync();
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error marking notification {QueueId} as failed", queueId);
        }
    }

    public async Task<int> GetQueueLengthAsync()
    {
        try
        {
            return (int)await _database.SortedSetLengthAsync(QUEUE_KEY);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting queue length");
            return 0;
        }
    }

    public async Task<List<NotificationQueueItem>> GetFailedNotificationsAsync(int count = 10)
    {
        try
        {
            var items = await _database.HashGetAllAsync(FAILED_KEY);
            var notifications = new List<NotificationQueueItem>();

            foreach (var item in items.Take(count))
            {
                var notification = JsonConvert.DeserializeObject<NotificationQueueItem>(item.Value!);
                if (notification != null)
                    notifications.Add(notification);
            }

            return notifications.OrderByDescending(n => n.QueuedAt).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting failed notifications");
            return new List<NotificationQueueItem>();
        }
    }

    public async Task RequeueFailedNotificationAsync(string queueId)
    {
        try
        {
            var json = await _database.HashGetAsync(FAILED_KEY, queueId);
            if (json.HasValue)
            {
                var item = JsonConvert.DeserializeObject<NotificationQueueItem>(json!);
                if (item != null)
                {
                    item.RetryCount = 0;
                    item.ErrorMessage = null;
                    item.ScheduledFor = null;
                    
                    var score = CalculateScore(item);
                    var updatedJson = JsonConvert.SerializeObject(item);

                    var transaction = _database.CreateTransaction();
                    _ = transaction.HashDeleteAsync(FAILED_KEY, queueId);
                    _ = transaction.SortedSetAddAsync(QUEUE_KEY, updatedJson, score);
                    
                    if (await transaction.ExecuteAsync())
                    {
                        _logger.LogInformation("Requeued failed notification {QueueId}", queueId);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error requeuing failed notification {QueueId}", queueId);
        }
    }

    public async Task<int> GetProcessingCountAsync()
    {
        try
        {
            return (int)await _database.HashLengthAsync(PROCESSING_KEY);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting processing count");
            return 0;
        }
    }

    public async Task<int> GetFailedCountAsync()
    {
        try
        {
            return (int)await _database.HashLengthAsync(FAILED_KEY);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting failed count");
            return 0;
        }
    }

    public async Task<int> RetryFailedNotificationsAsync()
    {
        try
        {
            var failedItems = await _database.HashGetAllAsync(FAILED_KEY);
            var retriedCount = 0;

            foreach (var item in failedItems)
            {
                var notification = JsonConvert.DeserializeObject<NotificationQueueItem>(item.Value!);
                if (notification != null)
                {
                    notification.RetryCount = 0;
                    notification.ErrorMessage = null;
                    notification.ScheduledFor = null;

                    var score = CalculateScore(notification);
                    var updatedJson = JsonConvert.SerializeObject(notification);

                    var transaction = _database.CreateTransaction();
                    _ = transaction.HashDeleteAsync(FAILED_KEY, item.Name);
                    _ = transaction.SortedSetAddAsync(QUEUE_KEY, updatedJson, score);

                    if (await transaction.ExecuteAsync())
                    {
                        retriedCount++;
                    }
                }
            }

            _logger.LogInformation("Retried {Count} failed notifications", retriedCount);
            return retriedCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrying failed notifications");
            return 0;
        }
    }

    public async Task<int> ClearFailedNotificationsAsync()
    {
        try
        {
            var failedCount = await GetFailedCountAsync();
            await _database.KeyDeleteAsync(FAILED_KEY);

            _logger.LogInformation("Cleared {Count} failed notifications", failedCount);
            return failedCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing failed notifications");
            return 0;
        }
    }

    private double CalculateScore(NotificationQueueItem item)
    {
        // Calculate score based on priority and scheduled time
        var baseTime = item.ScheduledFor ?? DateTime.UtcNow;
        var timestamp = ((DateTimeOffset)baseTime).ToUnixTimeSeconds();

        // Adjust for priority (higher priority = lower score = processed first)
        var priorityAdjustment = (int)item.Priority * -1000000; // Negative to prioritize higher values

        return timestamp + priorityAdjustment;
    }
}
