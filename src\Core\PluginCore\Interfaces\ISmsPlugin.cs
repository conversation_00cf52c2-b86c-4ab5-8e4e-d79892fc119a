using SmsContract.Models;
using PluginContract.Models;
using PluginContract.Interfaces;

namespace PluginCore.Interfaces;

/// <summary>
/// Defines the contract for SMS notification plugins.
/// </summary>
/// <remarks>
/// This interface extends the base notification plugin interface to provide
/// SMS-specific functionality including single and bulk SMS sending,
/// message tracking, delivery reports, and configuration validation.
/// </remarks>
public interface ISmsPlugin : INotificationPlugin
{
    /// <summary>
    /// Sends a single SMS message asynchronously.
    /// </summary>
    /// <param name="request">The SMS request containing recipient phone number and message content</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>A task that represents the asynchronous send operation, containing the notification response</returns>
    /// <exception cref="ArgumentNullException">Thrown when request is null</exception>
    /// <exception cref="InvalidOperationException">Thrown when the plugin is not properly configured</exception>
    /// <remarks>
    /// The phone number should be in international format (e.g., +**********).
    /// Message length limits vary by provider and may result in multiple SMS parts.
    /// </remarks>
    Task<NotificationResponse> SendAsync(SendSmsRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Sends multiple SMS messages in a single batch operation asynchronously.
    /// </summary>
    /// <param name="requests">The collection of SMS requests to send</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>A task that represents the asynchronous bulk send operation, containing the notification response</returns>
    /// <exception cref="ArgumentNullException">Thrown when requests is null</exception>
    /// <exception cref="ArgumentException">Thrown when requests collection is empty</exception>
    /// <exception cref="InvalidOperationException">Thrown when the plugin is not properly configured</exception>
    /// <remarks>
    /// Bulk operations are more efficient for sending multiple SMS messages and provide
    /// better error handling for individual message failures. Rate limiting may apply.
    /// </remarks>
    Task<NotificationResponse> SendBulkAsync(IEnumerable<SendSmsRequest> requests, CancellationToken cancellationToken = default);

    /// <summary>
    /// <param name="messageId">The unique identifier of the message to check</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>A task that represents the asynchronous status check operation, containing the notification response with status information</returns>
    /// <exception cref="ArgumentException">Thrown when messageId is null or empty</exception>
    /// <exception cref="InvalidOperationException">Thrown when the plugin is not properly configured</exception>
    /// <remarks>
    /// Status information may include delivery status, delivery timestamps, and failure reasons.
    /// Delivery reports depend on carrier support and may not be available for all messages.
    /// </remarks>
    Task<NotificationResponse> GetMessageStatusAsync(string messageId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Retrieves the message history for a specific phone number.
    /// </summary>
    /// <param name="phoneNumber">The phone number to retrieve history for (in international format)</param>
    /// <param name="pageSize">The maximum number of messages to return (optional)</param>
    /// <param name="pageToken">Token for pagination (optional)</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>A task that represents the asynchronous history retrieval operation, containing the notification response with message history</returns>
    /// <exception cref="ArgumentException">Thrown when phoneNumber is null, empty, or invalid</exception>
    /// <exception cref="InvalidOperationException">Thrown when the plugin is not properly configured</exception>
    /// <remarks>
    /// History includes sent messages, delivery status, and timestamps.
    /// Results may be paginated for large datasets. Phone number should be in international format.
    /// </remarks>
    Task<NotificationResponse> GetMessageHistoryAsync(string phoneNumber, int? pageSize = null, string? pageToken = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Resends a previously sent SMS message using the same content and recipient.
    /// </summary>
    /// <param name="messageId">The unique identifier of the message to resend</param>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>A task that represents the asynchronous resend operation, containing the notification response</returns>
    /// <exception cref="ArgumentException">Thrown when messageId is null or empty</exception>
    /// <exception cref="InvalidOperationException">Thrown when the plugin is not properly configured or the original message cannot be found</exception>
    /// <remarks>
    /// The resent message will have a new message ID but will use the same content as the original.
    /// Not all SMS providers support message resending.
    /// </remarks>
    Task<NotificationResponse> ResendMessageAsync(string messageId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates the current plugin configuration to ensure it's properly set up for sending SMS messages.
    /// </summary>
    /// <param name="cancellationToken">Token to cancel the operation</param>
    /// <returns>A task that represents the asynchronous validation operation, returning true if configuration is valid</returns>
    /// <remarks>
    /// This method should verify API keys, connection settings, sender ID configuration, and any other required settings.
    /// It may perform test API calls to validate connectivity and account status.
    /// </remarks>
    Task<bool> ValidateConfigurationAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the maximum message length supported by the provider in characters.
    /// </summary>
    /// <returns>The maximum message length, or null if unlimited</returns>
    /// <remarks>
    /// Standard SMS supports 160 characters for GSM 7-bit encoding or 70 characters for Unicode.
    /// Longer messages may be split into multiple parts.
    /// </remarks>
    int? MaxMessageLength { get; }

    /// <summary>
    /// Gets the maximum number of SMS messages that can be sent in a single bulk operation.
    /// </summary>
    /// <returns>The maximum bulk size, or null if unlimited</returns>
    int? MaxBulkSize { get; }

    /// <summary>
    /// Gets a value indicating whether the provider supports delivery reports and status tracking.
    /// </summary>
    bool SupportsDeliveryReports { get; }

    /// <summary>
    /// Gets a value indicating whether the provider supports message resending.
    /// </summary>
    bool SupportsResending { get; }

    /// <summary>
    /// Gets a value indicating whether the provider supports Unicode characters in messages.
    /// </summary>
    bool SupportsUnicode { get; }

    /// <summary>
    /// Gets the supported sender ID types for this provider.
    /// </summary>
    /// <returns>A collection of supported sender ID types (e.g., "Alphanumeric", "ShortCode", "LongCode")</returns>
    IEnumerable<string> SupportedSenderIdTypes { get; }
}
