using EmailService.Library.Configuration;
using EmailService.Library.Interfaces;
using EmailService.Library.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;

namespace EmailService.Library.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddEmailService(this IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<EmailServiceSettings>(configuration.GetSection("EmailService"));
        
        services.AddScoped<IEmailService, EmailSenderService>();
        services.AddScoped<ISmtpClient, SmtpClientService>();
        
        return services;
    }
}
