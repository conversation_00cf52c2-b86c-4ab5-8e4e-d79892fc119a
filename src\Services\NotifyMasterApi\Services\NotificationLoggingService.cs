using Microsoft.EntityFrameworkCore;
using NotificationContract.Enums;
using NotifyMasterApi.Infrastructure.Entities;
using NotifyMasterApi.Infrastructure.Interfaces;

namespace NotifyMasterApi.Services;

///<summary>
///  Service for logging notifications and errors
/// </summary>
/// <remarks>
/// This service provides methods for logging notifications and errors, including:
/// - Logging notifications
/// - Updating notification status
/// - Logging errors
/// - Retrieving notification logs
/// - Retrieving notification history
/// - Updating metrics
/// - Retrieving metrics
/// - Retrieving errors
/// </remarks>
/// <inheritdoc />
public interface INotificationLoggingService
{
    Task<string> LogNotificationAsync(NotificationType type, string recipient, string subject, string content, string? userId = null, string? correlationId = null, Dictionary<string, object>? metadata = null);
    Task UpdateNotificationStatusAsync(string messageId, NotificationStatus status, string? provider = null, string? errorMessage = null, string? responseData = null);
    Task LogErrorAsync(NotificationType type, string provider, string errorCode, string errorMessage, string? recipient = null, string? messageId = null, string? stackTrace = null, string? requestData = null, int severity = 2);
    Task<NotificationLog?> GetNotificationLogAsync(string messageId);
    Task<List<NotificationLog>> GetNotificationHistoryAsync(string recipient, int skip = 0, int take = 50);
    Task UpdateMetricsAsync(NotificationType type, string provider, bool success, double responseTime);
    Task<List<NotificationMetrics>> GetMetricsAsync(NotificationType? type = null, string? provider = null, DateTime? from = null, DateTime? to = null);
    Task<List<NotificationError>> GetErrorsAsync(NotificationType? type = null, string? provider = null, DateTime? from = null, DateTime? to = null, bool unresolved = false);
}
/// <inheritdoc />
public class NotificationLoggingService : INotificationLoggingService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<NotificationLoggingService> _logger;

    public NotificationLoggingService(IUnitOfWork unitOfWork, ILogger<NotificationLoggingService> logger)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

/// <summary>
///  Log a new notification
/// </summary>
/// <param name="type"> Type of notification </param>
/// <param name="recipient"> Recipient of the notification </param>
/// <param name="subject"> Subject of the notification </param>
/// <param name="content"> Content of the notification </param>
/// <param name="userId"> User ID associated with the notification </param>
/// <param name="correlationId"> Correlation ID for tracking </param>
/// <param name="metadata"> Metadata associated with the notification </param>
/// <returns> Message ID of the logged notification </returns>
    public async Task<string> LogNotificationAsync(NotificationType type, string recipient, string subject, string content, string? userId = null, string? correlationId = null, Dictionary<string, object>? metadata = null)
    {
        try
        {
            var messageId = Guid.NewGuid().ToString();
            var log = new NotificationLog
            {
                Id = Guid.NewGuid(),
                MessageId = messageId,
                Type = type,
                Recipient = recipient,
                Subject = subject,
                Content = content,
                Status = NotificationStatus.Pending,
                CreatedAt = DateTime.UtcNow,
                UserId = userId,
                CorrelationId = correlationId,
                Metadata = metadata
            };

            await _unitOfWork.Notifications.AddAsync(log);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Logged notification {MessageId} for {Type} to {Recipient}", messageId, type, recipient);
            return messageId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error logging notification for {Type} to {Recipient}", type, recipient);
            return Guid.NewGuid().ToString(); // Return a fallback ID
        }
    }

    public async Task UpdateNotificationStatusAsync(string messageId, NotificationStatus status, string? provider = null, string? errorMessage = null, string? responseData = null)
    {
        try
        {
            var log = await _unitOfWork.Notifications.GetByMessageIdAsync(messageId);
            if (log == null)
            {
                _logger.LogWarning("Notification log not found for message ID {MessageId}", messageId);
                return;
            }

            log.Status = status;
            log.Provider = provider ?? log.Provider;
            log.ErrorMessage = errorMessage;
            log.ResponseData = responseData != null ? new Dictionary<string, object> { { "response", responseData } } : log.ResponseData;

            switch (status)
            {
                case NotificationStatus.Processing:
                    // No specific timestamp for processing
                    break;
                case NotificationStatus.Sent:
                    log.SentAt = DateTime.UtcNow;
                    break;
                case NotificationStatus.Delivered:
                    log.DeliveredAt = DateTime.UtcNow;
                    break;
                case NotificationStatus.Failed:
                    log.FailedAt = DateTime.UtcNow;
                    break;
                case NotificationStatus.Retrying:
                    log.RetryCount++;
                    log.LastRetryAt = DateTime.UtcNow;
                    break;
            }

            await _unitOfWork.Notifications.UpdateAsync(log);
            await _unitOfWork.SaveChangesAsync();
            _logger.LogInformation("Updated notification {MessageId} status to {Status}", messageId, status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating notification status for {MessageId}", messageId);
        }
    }

    public async Task LogErrorAsync(NotificationType type, string provider, string errorCode, string errorMessage, string? recipient = null, string? messageId = null, string? stackTrace = null, string? requestData = null, int severity = 2)
    {
        try
        {
            var error = new NotificationError
            {
                Id = Guid.NewGuid(),
                Type = type,
                Provider = provider,
                ErrorCode = errorCode,
                ErrorMessage = errorMessage,
                Recipient = recipient,
                MessageId = messageId,
                StackTrace = stackTrace,
                RequestData = requestData,
                OccurredAt = DateTime.UtcNow,
                Severity = severity,
                IsResolved = false
            };

            await _unitOfWork.Errors.AddAsync(error);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogError("Logged error {ErrorCode} for {Type} provider {Provider}: {ErrorMessage}", errorCode, type, provider, errorMessage);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error logging notification error for {Type} provider {Provider}", type, provider);
        }
    }

    public async Task<NotificationLog?> GetNotificationLogAsync(string messageId)
    {
        try
        {
            return await _unitOfWork.Notifications.GetByMessageIdAsync(messageId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting notification log for {MessageId}", messageId);
            return null;
        }
    }

    public async Task<List<NotificationLog>> GetNotificationHistoryAsync(string recipient, int skip = 0, int take = 50)
    {
        try
        {
            return await _unitOfWork.Notifications.GetAsync(
                filter: l => l.Recipient == recipient,
                orderBy: q => q.OrderByDescending(l => l.CreatedAt),
                skip: skip,
                take: take);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting notification history for {Recipient}", recipient);
            return new List<NotificationLog>();
        }
    }

    public async Task UpdateMetricsAsync(NotificationType type, string provider, bool success, double responseTime)
    {
        try
        {
            var today = DateTime.UtcNow.Date;
            var existingMetrics = await _unitOfWork.Metrics.GetAsync(
                filter: m => m.Type == type && m.Provider == provider && m.Date == today,
                take: 1);

            var metrics = existingMetrics.FirstOrDefault();

            if (metrics == null)
            {
                metrics = new NotificationMetrics
                {
                    Id = Guid.NewGuid(),
                    Type = type,
                    Provider = provider,
                    Date = today,
                    CreatedAt = DateTime.UtcNow
                };
                await _unitOfWork.Metrics.AddAsync(metrics);
            }

            metrics.TotalSent++;
            if (success)
            {
                metrics.TotalDelivered++;
            }
            else
            {
                metrics.TotalFailed++;
            }

            // Update average response time
            var totalResponses = metrics.TotalSent;
            metrics.AverageResponseTime = ((metrics.AverageResponseTime * (totalResponses - 1)) + responseTime) / totalResponses;

            // Update rates
            metrics.SuccessRate = (double)metrics.TotalDelivered / metrics.TotalSent * 100;
            metrics.FailureRate = (double)metrics.TotalFailed / metrics.TotalSent * 100;
            metrics.UpdatedAt = DateTime.UtcNow;

            if (existingMetrics.Any())
            {
                await _unitOfWork.Metrics.UpdateAsync(metrics);
            }

            await _unitOfWork.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating metrics for {Type} provider {Provider}", type, provider);
        }
    }

    public async Task<List<NotificationMetrics>> GetMetricsAsync(NotificationType? type = null, string? provider = null, DateTime? from = null, DateTime? to = null)
    {
        try
        {
            return await _unitOfWork.Metrics.GetAsync(
                filter: m => (!type.HasValue || m.Type == type.Value) &&
                           (string.IsNullOrEmpty(provider) || m.Provider == provider) &&
                           (!from.HasValue || m.Date >= from.Value.Date) &&
                           (!to.HasValue || m.Date <= to.Value.Date),
                orderBy: q => q.OrderByDescending(m => m.Date));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting metrics");
            return new List<NotificationMetrics>();
        }
    }

    public async Task<List<NotificationError>> GetErrorsAsync(NotificationType? type = null, string? provider = null, DateTime? from = null, DateTime? to = null, bool unresolved = false)
    {
        try
        {
            return await _unitOfWork.Errors.GetAsync(
                filter: e => (!type.HasValue || e.Type == type.Value) &&
                           (string.IsNullOrEmpty(provider) || e.Provider == provider) &&
                           (!from.HasValue || e.OccurredAt >= from.Value) &&
                           (!to.HasValue || e.OccurredAt <= to.Value) &&
                           (!unresolved || !e.IsResolved),
                orderBy: q => q.OrderByDescending(e => e.OccurredAt));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting errors");
            return new List<NotificationError>();
        }
    }
}
